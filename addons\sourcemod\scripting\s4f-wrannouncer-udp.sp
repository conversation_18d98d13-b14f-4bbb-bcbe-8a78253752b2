#include <shavit>
#include <socket>
#include <smjansson>
#include <sdktools>    // For GetCurrentMap, GetClientName, GetGameTime
#pragma newdecls required
// --- CVars ---
ConVar           g_cvListenPort;
ConVar           g_cvServerID;
ConVar           g_cvSharedSecret;
ConVar           g_cvPeerServers;

// --- Globals ---
Socket           g_hWRListenerSocket;
char             g_sThisServerID[64];
char             g_sSharedSecretKey[128];
ArrayList        g_alPeerIPs;      // Stores peer IP strings
ArrayList        g_alPeerPorts;    // Stores peer Port integers

chatstrings_t    gS_ChatStrings;

// Simple checksum function for basic message authentication
int              CalculateSimpleChecksum(const char[] str)
{
    int checksum = 0;
    for (int i = 0; str[i] != '\0'; i++)
    {
        checksum = (checksum + str[i] * (i + 1)) % 65536;    // A bit more variance
    }
    return checksum;
}

public Plugin myinfo =
{
    name        = "S4F - WR Announcer",
    author      = "Proxychains who?",
    description = "Announces new world records across multiple servers via UDP.",
    version     = "1.1",
    url         = "https://github.com/surfing4fun"
};

public void OnPluginStart()
{
    LoadTranslations("shavit-common.phrases");    // For chat colors

    g_cvListenPort   = CreateConVar("sm_globalwr_listen_port", "27025", "UDP port this server will listen on for WR announcements.");
    g_cvServerID     = CreateConVar("sm_globalwr_server_id", "MyServer_1", "Unique ID for this server.");
    g_cvSharedSecret = CreateConVar("sm_globalwr_shared_secret", "ChangeMePlease", "Shared secret key for authenticating messages between servers.");
    g_cvPeerServers  = CreateConVar("sm_globalwr_peers", "", "Comma-separated list of peer servers (ip:port,ip:port). E.g., '*******:27025,*******:27025'");

    AutoExecConfig(true, "plugin.s4f-wrannouncer");

    g_cvListenPort.AddChangeHook(OnConVarChanged);
    g_cvServerID.AddChangeHook(OnConVarChanged);
    g_cvSharedSecret.AddChangeHook(OnConVarChanged);
    g_cvPeerServers.AddChangeHook(OnConVarChanged);

    // Initial CVar setup
    ApplyCVarSettings();

    // Initialize peer lists
    g_alPeerIPs   = new ArrayList(64);    // Max IP string length
    g_alPeerPorts = new ArrayList(4);     // sizeof(int) is 4 bytes
    ParsePeerServers();

    // Setup UDP Listener
    SetupSocketListener();
}

public void Shavit_OnChatConfigLoaded()
{
    Shavit_GetChatStrings(sMessageText, gS_ChatStrings.sText, sizeof(chatstrings_t::sText));
    Shavit_GetChatStrings(sMessageStyle, gS_ChatStrings.sStyle, sizeof(chatstrings_t::sStyle));
    Shavit_GetChatStrings(sMessageWarning, gS_ChatStrings.sWarning, sizeof(chatstrings_t::sWarning));
    Shavit_GetChatStrings(sMessageVariable, gS_ChatStrings.sVariable, sizeof(chatstrings_t::sVariable));
    Shavit_GetChatStrings(sMessageVariable, gS_ChatStrings.sVariable2, sizeof(chatstrings_t::sVariable2));
}

public void OnConVarChanged(ConVar convar, const char[] oldValue, const char[] newValue)
{
    if (convar == g_cvListenPort || convar == g_cvServerID || convar == g_cvSharedSecret)
    {
        ApplyCVarSettings();
        // If listen port changed, we might need to restart the socket
        if (convar == g_cvListenPort)
        {
            CloseSocketListener();
            SetupSocketListener();
        }
    }
    else if (convar == g_cvPeerServers) {
        ParsePeerServers();
    }
}

void ApplyCVarSettings()
{
    g_cvServerID.GetString(g_sThisServerID, sizeof(g_sThisServerID));
    g_cvSharedSecret.GetString(g_sSharedSecretKey, sizeof(g_sSharedSecretKey));
}

void ParsePeerServers()
{
    if (g_alPeerIPs == null || g_alPeerPorts == null) return;

    g_alPeerIPs.Clear();
    g_alPeerPorts.Clear();

    char sPeerList[1024];
    g_cvPeerServers.GetString(sPeerList, sizeof(sPeerList));

    if (StrEqual(sPeerList, "")) return;

    char sPeers[MAXPLAYERS + 1][64];    // Max peers roughly MaxPlayers for sanity
    int  numPeers = ExplodeString(sPeerList, ",", sPeers, sizeof(sPeers), sizeof(sPeers[]));

    for (int i = 0; i < numPeers; i++)
    {
        TrimString(sPeers[i]);
        char sIpPort[2][32];
        if (ExplodeString(sPeers[i], ":", sIpPort, sizeof(sIpPort), sizeof(sIpPort[])) == 2)
        {
            TrimString(sIpPort[0]);    // IP
            TrimString(sIpPort[1]);    // Port
            g_alPeerIPs.PushString(sIpPort[0]);
            g_alPeerPorts.Push(StringToInt(sIpPort[1]));
            // LogMessage("[GlobalWRAnnounce] Added peer: %s:%d", sIpPort[0], StringToInt(sIpPort[1]));
        }
        else {
            LogError("[GlobalWRAnnounce] Invalid peer format (expected ip:port): %s", sPeers[i]);
        }
    }
    if (g_alPeerIPs.Length != g_alPeerPorts.Length)
    {    // Sanity check
        LogError("[GlobalWRAnnounce] Peer IP and Port list mismatch after parsing. Clearing peers.");
        g_alPeerIPs.Clear();
        g_alPeerPorts.Clear();
    }
    LogMessage("[GlobalWRAnnounce] Loaded %d peer servers.", g_alPeerIPs.Length);
}

void SetupSocketListener()
{
    if (g_hWRListenerSocket != null)
    {
        CloseHandle(g_hWRListenerSocket);
        g_hWRListenerSocket = null;
        // LogMessage("[GlobalWRAnnounce] Previous listener socket closed.");
    }

    char sListenPort[8];
    g_cvListenPort.GetString(sListenPort, sizeof(sListenPort));
    int port            = StringToInt(sListenPort);

    // Use the methodmap constructor from your socket.inc
    g_hWRListenerSocket = new Socket(SOCKET_UDP, OnWRSocketError);
    if (g_hWRListenerSocket == null)
    {
        SetFailState("[GlobalWRAnnounce] Failed to create UDP listener socket.");
        return;
    }
    if (!g_hWRListenerSocket.Bind("0.0.0.0", port))    // Bind to all interfaces on the specified port
    {
        LogError("[GlobalWRAnnounce] Failed to bind UDP socket to port %d. Check for conflicts or permission issues.", port);
        CloseHandle(g_hWRListenerSocket);
        g_hWRListenerSocket = null;
        // Provide a more descriptive fail state message
        char failMsg[256];
        FormatEx(failMsg, sizeof(failMsg), "[GlobalWRAnnounce] Socket bind failed for port %d. Port may be in use, invalid, or check server permissions.", port);
        SetFailState(failMsg);
        return;
    }

    g_hWRListenerSocket.SetReceiveCallback(OnWRDataReceived);
    LogMessage("[GlobalWRAnnounce] Listening for WR announcements on UDP port %d.", port);
}

void CloseSocketListener()
{
    if (g_hWRListenerSocket != null)
    {
        CloseHandle(g_hWRListenerSocket);
        g_hWRListenerSocket = null;
        LogMessage("[GlobalWRAnnounce] UDP listener socket closed.");
    }
}

public void OnPluginEnd()
{
    CloseSocketListener();
    if (g_alPeerIPs != null)
    {
        delete g_alPeerIPs;
        g_alPeerIPs = null;
    }
    if (g_alPeerPorts != null)
    {
        delete g_alPeerPorts;
        g_alPeerPorts = null;
    }
}

// Matches: typedef SocketErrorCB = function void (Socket socket, const int errorType, const int errorNum, any arg);
// The 'message' parameter is not part of this specific socket.inc's error callback.
public void OnWRSocketError(Socket socket, int errorType, int errorNum, any arg)
{
    LogError("[GlobalWRAnnounce] Socket error: Type %d, Code %d (errno). Socket Handle: %d", errorType, errorNum, view_as<Handle>(socket));
}

// Matches: typedef SocketReceiveCB = function void (Socket socket, const char[] receiveData, const int dataSize, any arg);
// This callback does not provide the sender's Address (IP/Port).
public void OnWRDataReceived(Socket socket, const char[] data, int dataLen, any arg)
{
    // Use json_loadb to parse the string data into a JSON Handle
    // The third argument 0 means no special flags.
    // The fourth argument null means no error reporting structure.
    // Switching to json_load as json_loadb seems undefined in the current smjansson.inc
    Handle receivedJsonHandle = json_load(data);    // data should be null-terminated from sender
    if (receivedJsonHandle == null)
    {
        LogError("[GlobalWRAnnounce] Failed to parse JSON. Data: %s", data);
        return;
    }

    char   remote_server_id[64];
    char   remote_player_name[MAX_NAME_LENGTH];
    char   remote_map_name[PLATFORM_MAX_PATH];
    int    remote_style          = 0;
    int    remote_track          = 0;
    float  remote_time           = 0.0;
    int    received_checksum     = 0;
    int    time_int_for_checksum = 0;

    Handle hValue;

    // Get server_id
    hValue = json_object_get(receivedJsonHandle, "server_id");
    if (hValue != null && json_is_string(hValue))
    {
        json_string_value(hValue, remote_server_id, sizeof(remote_server_id));
    }
    CloseHandle(hValue);    // Release the reference from json_object_get

    // Get player_name
    hValue = json_object_get(receivedJsonHandle, "player_name");
    if (hValue != null && json_is_string(hValue))
    {
        json_string_value(hValue, remote_player_name, sizeof(remote_player_name));
    }
    CloseHandle(hValue);

    // Get map_name
    hValue = json_object_get(receivedJsonHandle, "map_name");
    if (hValue != null && json_is_string(hValue))
    {
        json_string_value(hValue, remote_map_name, sizeof(remote_map_name));
    }
    CloseHandle(hValue);

    // Get style
    hValue = json_object_get(receivedJsonHandle, "style");
    if (hValue != null && json_is_integer(hValue))
    {
        remote_style = json_integer_value(hValue);
    }
    CloseHandle(hValue);

    // Get track
    hValue = json_object_get(receivedJsonHandle, "track");
    if (hValue != null && json_is_integer(hValue))
    {
        remote_track = json_integer_value(hValue);
    }
    CloseHandle(hValue);

    // Get time_int (for checksum and conversion)
    hValue = json_object_get(receivedJsonHandle, "time_int");
    if (hValue != null && json_is_integer(hValue))
    {
        time_int_for_checksum = json_integer_value(hValue);
        remote_time           = float(time_int_for_checksum) / 1000.0;
    }
    CloseHandle(hValue);

    // float remote_timestamp = view_as<float>(receivedJson.GetInteger("timestamp_int")) / 1000.0; // Optional

    // Get auth_checksum
    hValue = json_object_get(receivedJsonHandle, "auth_checksum");
    if (hValue != null && json_is_integer(hValue))
    {
        received_checksum = json_integer_value(hValue);
    }
    CloseHandle(hValue);

    // Validate: server_id (don't announce our own messages)
    if (StrEqual(remote_server_id, g_sThisServerID, false))
    {
        CloseHandle(receivedJsonHandle);    // Release the root JSON object
        return;                             // Message from self, ignore
    }

    // Validate: auth_checksum
    char dataToVerify[512];                                               // Ensure this is large enough
    Format(dataToVerify, sizeof(dataToVerify), "%s|%s|%s|%d|%d|%d|%s",    // Use time_int for checksum consistency
           remote_server_id, remote_player_name, remote_map_name, remote_style, remote_track, time_int_for_checksum, g_sSharedSecretKey);
    int expected_checksum = CalculateSimpleChecksum(dataToVerify);

    if (received_checksum != expected_checksum)
    {
        LogError("[GlobalWRAnnounce] Auth checksum mismatch. Expected %d, got %d. Data: %s", expected_checksum, received_checksum, data);
        CloseHandle(receivedJsonHandle);
        return;
    }

    // Validate: map (don't announce if it's for the current map)
    char currentMap[PLATFORM_MAX_PATH];
    GetCurrentMap(currentMap, sizeof(currentMap));
    if (StrEqual(remote_map_name, currentMap, false))
    {
        CloseHandle(receivedJsonHandle);
        return;    // WR is for the current map, Shavit handles local announcements
    }

    // All checks passed, announce it
    AnnounceWR(remote_player_name, remote_map_name, remote_style, remote_track, remote_time);
    CloseHandle(receivedJsonHandle);    // Release the root JSON object
}

void AnnounceWR(const char[] playername, const char[] mapname, int style, int track, float rectime)
{
    char styleString[32] = "Normal";
    if (style != 0)
    {
        FormatEx(styleString, sizeof(styleString), "%d", style);
    }

    char trackString[32] = "Main";
    if (track != 0)
    {
        FormatEx(trackString, sizeof(trackString), "Bonus %d", track);
    }

    // Format the announcement message
    char buffer[256];
    FormatEx(buffer, sizeof(buffer),
             "%s[New World Record] %s%s %sfinished %s%s %s(%s, Style: %s) in %s%.3fs%s!",
             gS_ChatStrings.sWarning,
             gS_ChatStrings.sVariable2,
             playername,
             gS_ChatStrings.sText,
             gS_ChatStrings.sVariable,
             mapname,
             gS_ChatStrings.sStyle,
             trackString,
             styleString,
             gS_ChatStrings.sVariable,
             rectime,
             gS_ChatStrings.sText);

    // Log to server console
    PrintToServer("[GlobalWR] %s on %s (Track: %s, Style: %s): %.3fs",
                  playername, mapname, trackString, styleString, rectime);

    // Announce to all players
    Shavit_PrintToChatAll(buffer);
}

// Called by Shavit's core when a player sets a new world record on this server
public void Shavit_OnWorldRecord(int client, int style, float time, int jumps, int strafes, float sync, int track, int stage, float oldwr)
{
    // We only care about full runs (not individual stages) for this global announcer
    if (stage != 0) return;                                        // Only full runs
    if (g_alPeerIPs == null || g_alPeerIPs.Length == 0) return;    // No peers to send to
    if (g_hWRListenerSocket == null) return;                       // Socket not ready

    char playerName[MAX_NAME_LENGTH];
    GetClientName(client, playerName, sizeof(playerName));
    char mapName[PLATFORM_MAX_PATH];
    GetCurrentMap(mapName, sizeof(mapName));

    // To avoid float precision issues in checksum/JSON, send time as integer milliseconds
    int    time_ms    = RoundToNearest(time * 1000.0);
    // int timestamp_ms = RoundToNearest(GetGameTime() * 1000.0); // Optional

    // Create a new JSON object
    Handle jsonHandle = json_object();
    if (jsonHandle == null)
    {
        LogError("[GlobalWRAnnounce] Failed to create JSON object for broadcast.");
        return;
    }

    Handle hVal;    // Temporary handle for created JSON values

    // Add server_id
    hVal = json_string(g_sThisServerID);
    json_object_set(jsonHandle, "server_id", hVal);
    CloseHandle(hVal);    // We created hVal, json_object_set took its own ref, so we close ours.

    // Add player_name
    hVal = json_string(playerName);
    json_object_set(jsonHandle, "player_name", hVal);
    CloseHandle(hVal);

    // Add map_name
    hVal = json_string(mapName);
    json_object_set(jsonHandle, "map_name", hVal);
    CloseHandle(hVal);

    // Add style
    hVal = json_integer(style);
    json_object_set(jsonHandle, "style", hVal);
    CloseHandle(hVal);

    // Add track
    hVal = json_integer(track);
    json_object_set(jsonHandle, "track", hVal);
    CloseHandle(hVal);

    // Add time_int
    hVal = json_integer(time_ms);
    json_object_set(jsonHandle, "time_int", hVal);
    CloseHandle(hVal);

    // Create data string for checksum
    char dataToSign[512];    // Ensure this is large enough
    Format(dataToSign, sizeof(dataToSign), "%s|%s|%s|%d|%d|%d|%s",
           g_sThisServerID, playerName, mapName, style, track, time_ms, g_sSharedSecretKey);
    int checksum = CalculateSimpleChecksum(dataToSign);
    hVal         = json_integer(checksum);
    json_object_set(jsonHandle, "auth_checksum", hVal);
    CloseHandle(hVal);

    char sJsonBuffer[1024];    // Buffer for JSON string
    // JSON_COMPACT is not defined in the provided smjansson.inc. Using 0 for flags.
    // This usually produces compact output without extra indentation.
    // json_dump returns 0 on success.
    if (json_dump(jsonHandle, sJsonBuffer, sizeof(sJsonBuffer), 0) == 0)
    {
        char peerIP[64];
        int  peerPort;
        for (int i = 0; i < g_alPeerIPs.Length; i++)
        {
            g_alPeerIPs.GetString(i, peerIP, sizeof(peerIP));
            peerPort = g_alPeerPorts.Get(i);
            g_hWRListenerSocket.SendTo(sJsonBuffer, strlen(sJsonBuffer), peerIP, peerPort);
        }
        // LogMessage("[GlobalWRAnnounce] Broadcasted WR: %s", sJsonBuffer);
    }
    else {
        LogError("[GlobalWRAnnounce] Failed to dump JSON for WR broadcast.");
    }
    CloseHandle(jsonHandle);    // Release the root JSON object
}