#pragma semicolon 1
#pragma newdecls required

#include <sourcemod>
#include <sdktools>
#include <shavit/core>

#define PLUGIN_VERSION      "1.2"
#define LINE_TIMER_INTERVAL 0.3    // Used for beam life calculation, must match timer creation
#define MAX_LINE_SETS       4
#define POINT_A             0
#define POINT_B             1

// Globals for points
float g_Points[MAXPLAYERS + 1][MAX_LINE_SETS][2][3];    // [client][set_idx][A_or_B][x_y_z]
bool  g_PointSet[MAXPLAYERS + 1][MAX_LINE_SETS][2];     // [client][set_idx][A_or_B]

// Globals for beam properties
int   g_PlayerBeamColor[MAXPLAYERS + 1][4];
float g_PlayerBeamWidth[MAXPLAYERS + 1];

enum LineVisibility
{
    LineVisibility_Self,
    LineVisibility_Everyone
} LineVisibility g_PlayerLineVisibility[MAXPLAYERS + 1];

int              g_LaserSprite;
Handle           g_hBeamTimer = null;

int              g_MenuData_LineSet_SetIdx[MAXPLAYERS + 1];

// Predefined colors
int              g_BeamColorOptions[][4] = {
    {255,  0,   0,   255}, // Red
    { 0,   255, 0,   255}, // Green
    { 0,   0,   255, 255}, // Blue
    { 255, 255, 0,   255}, // Yellow
    { 0,   255, 255, 255}, // Cyan
    { 255, 0,   255, 255}, // Magenta
    { 255, 255, 255, 255}  // White
};
char g_sBeamColorNames[][16] = {
    "Red",
    "Green",
    "Blue",
    "Yellow",
    "Cyan",
    "Magenta",
    "White"
};

const int     C_NUM_COLOR_OPTIONS  = sizeof(g_sBeamColorNames);
float         g_BeamWidthOptions[] = { 0.5, 1.5, 2.5, 4.0, 6.0, 8.0 };
const int     C_NUM_WIDTH_OPTIONS  = sizeof(g_BeamWidthOptions);

chatstrings_t gS_ChatStrings;

public Plugin myinfo =
{
    name        = "S4F - Line Drawer",
    author      = "Proxychains",
    description = "Allows VIPs to draw lines between two points to help w/ routing.",
    version     = PLUGIN_VERSION,
    url         = "https://github.com/surfing4fun"
};

public void OnPluginStart()
{
    RegAdminCmd("sm_line", Command_LineDrawer, ADMFLAG_CUSTOM1, "Opens the line drawer menu.");
    RegAdminCmd("sm_lines", Command_LineDrawer, ADMFLAG_CUSTOM1, "Opens the line drawer menu.");

    g_LaserSprite = PrecacheModel("shavit/laserbeam.vmt");
    if (g_LaserSprite == 0)
    {
        g_LaserSprite = PrecacheModel("materials/sprites/laserbeam.vmt");    // Fallback
    }

    if (g_LaserSprite == 0)
    {
        LogError("[Lines] OnPluginStart: CRITICAL - Failed to precache laserbeam sprite. Plugin visuals will not work.");
    }
    else
    {
        LogMessage("[Lines] OnPluginStart: Successfully precached sprite. ID: %d", g_LaserSprite);
    }

    // Ensure any old timer is killed before creating a new one (robustness for plugin reloads).
    // Timers without TIMER_FLAG_NO_MAPCHANGE are killed on map change automatically.
    Handle old_plugin_start_timer_handle = g_hBeamTimer;
    g_hBeamTimer                         = null;    // Immediately nullify the global handle
    if (old_plugin_start_timer_handle != null && old_plugin_start_timer_handle != INVALID_HANDLE)
    {
        KillTimer(old_plugin_start_timer_handle);    // Use the stored handle
    }                                                // Create the timer. It will be killed automatically on map change.
    // It will be recreated in OnMapStart.
    g_hBeamTimer = CreateTimer(LINE_TIMER_INTERVAL, Timer_DrawBeams, _, TIMER_REPEAT);

    for (int i = 1; i <= MaxClients; i++)
    {
        if (IsClientInGame(i))
        {
            InitializeClientData(i);
        }
    }
}

public void OnMapStart()
{
    // Re-precache sprite
    g_LaserSprite = PrecacheModel("shavit/laserbeam.vmt");
    LogMessage("[Lines] OnMapStart: PrecacheModel(\"shavit/laserbeam.vmt\") returned %d.", g_LaserSprite);
    if (g_LaserSprite == 0)
    {
        g_LaserSprite = PrecacheModel("materials/sprites/laserbeam.vmt");
        // LogMessage("[Lines] OnMapStart: Fallback PrecacheModel returned %d.", g_LaserSprite);
    }

    if (g_LaserSprite == 0)
    {
        LogError("[Lines] OnMapStart: CRITICAL - Failed to precache laserbeam sprite. Visuals will not work this map.");
    }
    else
        LogMessage("[Lines] OnMapStart: Successfully precached sprite. ID: %d", g_LaserSprite);

    // The timer is killed automatically on map change because TIMER_FLAG_NO_MAPCHANGE is not used.
    // We must recreate it here after precaching the map-specific sprite.
    // Ensure we don't create a duplicate if OnMapStart is somehow called multiple times or the timer wasn't killed.
    if (g_hBeamTimer == null || g_hBeamTimer == INVALID_HANDLE)
    {
        g_hBeamTimer = CreateTimer(LINE_TIMER_INTERVAL, Timer_DrawBeams, _, TIMER_REPEAT);
        LogMessage("[Lines] OnMapStart: Created beam drawing timer.");
        // (which is re-precached above) on its next execution.
        // Thus, there's no need to kill and recreate the timer here.

        // Reset all data for all potential clients
        for (int i = 1; i <= MaxClients; i++)
        {
            InitializeClientData(i);
        }
    }
}

public void OnPluginEnd()
{
    if (g_hBeamTimer != null && g_hBeamTimer != INVALID_HANDLE)
    {
        KillTimer(g_hBeamTimer);
        g_hBeamTimer = null;
    }
}

public void Shavit_OnChatConfigLoaded()
{
    Shavit_GetChatStrings(sMessageText, gS_ChatStrings.sText, sizeof(chatstrings_t::sText));
    Shavit_GetChatStrings(sMessageStyle, gS_ChatStrings.sStyle, sizeof(chatstrings_t::sStyle));
    Shavit_GetChatStrings(sMessageWarning, gS_ChatStrings.sWarning, sizeof(chatstrings_t::sWarning));
    Shavit_GetChatStrings(sMessageVariable, gS_ChatStrings.sVariable, sizeof(chatstrings_t::sVariable));
}

public void OnClientPutInServer(int client)
{
    InitializeClientData(client);
}

public void OnClientDisconnect(int client)
{
    InitializeClientData(client);    // Clear their lines and reset data
}

void InitializeClientData(int client)
{
    if (!IsValidClient(client, false)) return;

    g_PlayerBeamColor[client]      = g_BeamColorOptions[C_NUM_COLOR_OPTIONS - 1];    // Default color: White
    g_PlayerBeamWidth[client]      = 1.5;                                            // Default width: 1.0
    g_PlayerLineVisibility[client] = LineVisibility_Self;                            // Default to self only

    ClearAllClientLines(client);
}

void ClearAllClientLines(int client)
{
    if (!IsValidClient(client, false)) return;
    for (int i = 0; i < MAX_LINE_SETS; i++)
    {
        ClearLineSet(client, i);
    }
}

void ClearLineSet(int client, int set_idx)
{
    if (!IsValidClient(client, false)) return;
    if (set_idx < 0 || set_idx >= MAX_LINE_SETS) return;

    g_PointSet[client][set_idx][POINT_A] = false;
    g_PointSet[client][set_idx][POINT_B] = false;
    for (int j = 0; j < 3; j++)
    {
        g_Points[client][set_idx][POINT_A][j] = 0.0;
        g_Points[client][set_idx][POINT_B][j] = 0.0;
    }
}

public Action Command_LineDrawer(int client, int args)
{
    if (!IsValidClient(client)) return Plugin_Handled;
    ShowMainMenu(client);
    return Plugin_Handled;
}

void ShowMainMenu(int client)
{
    if (!IsValidClient(client, false)) return;

    Menu menu = new Menu(MenuHandler_Main);
    menu.SetTitle("Line Drawer Menu");

    char sBuffer[128];
    for (int i = 0; i < MAX_LINE_SETS; i++)
    {
        char sStatus[32];
        if (g_PointSet[client][i][POINT_A] && g_PointSet[client][i][POINT_B])
        {
            FormatEx(sStatus, sizeof(sStatus), "(Active)");
        }
        else if (g_PointSet[client][i][POINT_A] || g_PointSet[client][i][POINT_B]) {
            FormatEx(sStatus, sizeof(sStatus), "(Partial)");
        }
        else {
            FormatEx(sStatus, sizeof(sStatus), "(Empty)");
        }
        FormatEx(sBuffer, sizeof(sBuffer), "Manage Line Set %d %s", i + 1, sStatus);
        menu.AddItem(FormatInt(i), sBuffer);    // Store set_idx as item info
    }

    char sCurrentColorName[32] = "Custom";    // Fallback if color isn't in predefined list
    for (int i = 0; i < C_NUM_COLOR_OPTIONS; i++)
    {
        if (g_PlayerBeamColor[client][0] == g_BeamColorOptions[i][0] && g_PlayerBeamColor[client][1] == g_BeamColorOptions[i][1] && g_PlayerBeamColor[client][2] == g_BeamColorOptions[i][2] && g_PlayerBeamColor[client][3] == g_BeamColorOptions[i][3])
        {
            strcopy(sCurrentColorName, sizeof(sCurrentColorName), g_sBeamColorNames[i]);
            break;
        }
    }
    FormatEx(sBuffer, sizeof(sBuffer), "Beam Color (Current: %s)", sCurrentColorName);
    menu.AddItem("color", sBuffer);

    FormatEx(sBuffer, sizeof(sBuffer), "Beam Width (Current: %.1f)", g_PlayerBeamWidth[client]);
    menu.AddItem("width", sBuffer);

    char sVisibility[64];
    FormatEx(sVisibility, sizeof(sVisibility), "Line Visibility (Current: %s)",
             g_PlayerLineVisibility[client] == LineVisibility_Self ? "Self Only" : "Everyone");
    menu.AddItem("visibility", sVisibility);

    menu.AddItem("clear_all", "Clear All My Lines");

    menu.ExitButton = true;
    menu.Display(client, MENU_TIME_FOREVER);
}

public int MenuHandler_Main(Menu menu, MenuAction action, int client, int param2_item_or_reason)
{
    if (action == MenuAction_Select)
    {
        if (!IsValidClient(client, false))
        {
            delete menu;
            return 0;
        }
        char sInfo[32];
        menu.GetItem(param2_item_or_reason, sInfo, sizeof(sInfo));    // param2 is itemNum for MenuAction_Select

        if (StrEqual(sInfo, "color"))
        {
            ShowColorMenu(client);
        }
        else if (StrEqual(sInfo, "width")) {
            ShowWidthMenu(client);
        }
        else if (StrEqual(sInfo, "clear_all")) {
            Shavit_PrintToChat(client, "%s[Lines] All your lines have been cleared.", gS_ChatStrings.sWarning);
            ClearAllClientLines(client);
            ShowMainMenu(client);
        }
        else if (StrEqual(sInfo, "visibility")) {
            ShowVisibilityMenu(client);
        }
        else {
            int set_idx = StringToInt(sInfo);
            if (set_idx >= 0 && set_idx < MAX_LINE_SETS)
            {
                ShowLineSetMenu(client, set_idx);
            }
        }
    }
    else if (action == MenuAction_Cancel)
    {
        // For the main menu, menu.ExitButton = true handles user selecting "Exit".
        // If ESC is pressed (MenuCancel_Cancelled) or client disconnects (MenuCancel_Disconnect),
        // MenuAction_End will handle cleanup. No specific action needed here for main menu's cancel.
    }
    else if (action == MenuAction_End) {
        delete menu;
    }
    return 0;
}

void ShowLineSetMenu(int client, int set_idx)
{
    if (!IsValidClient(client, false)) return;

    Menu menu = new Menu(MenuHandler_LineSet);
    char title[64];
    FormatEx(title, sizeof(title), "Manage Line Set %d", set_idx + 1);
    menu.SetTitle(title);
    // menu.SetData(view_as<any>(set_idx)); // Modern SM 1.7+ way
    g_MenuData_LineSet_SetIdx[client] = set_idx;    // Workaround for older SM versions

    char sBuffer[128];
    FormatEx(sBuffer, sizeof(sBuffer), "Set Point A %s", g_PointSet[client][set_idx][POINT_A] ? "(Set)" : "");
    menu.AddItem("set_a", sBuffer);

    FormatEx(sBuffer, sizeof(sBuffer), "Set Point B %s", g_PointSet[client][set_idx][POINT_B] ? "(Set)" : "");
    menu.AddItem("set_b", sBuffer);

    FormatEx(sBuffer, sizeof(sBuffer), "Clear This Line (Set %d)", set_idx + 1);
    menu.AddItem("clear_set", sBuffer);

    menu.ExitBackButton = true;
    menu.Display(client, MENU_TIME_FOREVER);
}

public int MenuHandler_LineSet(Menu menu, MenuAction action, int client, int param2_item)
{
    if (action == MenuAction_Select)
    {
        if (!IsValidClient(client, false))
        {
            delete menu;    // Clean up this menu instance
            return 0;
        }
        // Client is valid, safe to access g_MenuData_LineSet_SetIdx
        int  set_idx = g_MenuData_LineSet_SetIdx[client];

        char sInfo[32];
        menu.GetItem(param2_item, sInfo, sizeof(sInfo));

        if (StrEqual(sInfo, "set_a"))
        {
            SetLinePoint(client, set_idx, POINT_A);
            ShowLineSetMenu(client, set_idx);
        }
        else if (StrEqual(sInfo, "set_b")) {
            SetLinePoint(client, set_idx, POINT_B);
            ShowLineSetMenu(client, set_idx);
        }
        else if (StrEqual(sInfo, "clear_set")) {
            ClearLineSet(client, set_idx);
            Shavit_PrintToChat(client, "%s[Lines] Set %d cleared.", gS_ChatStrings.sText, set_idx + 1);

            ShowLineSetMenu(client, set_idx);
        }
    }
    else if (action == MenuAction_Cancel)
    {
        int reason = param2_item;    // param2 is MenuCancelReason
        if (reason == MenuCancel_ExitBack)
        {
            // Ensure client is still valid before attempting to show another menu
            if (IsValidClient(client, false))
            {
                ShowMainMenu(client);
            }
        }
    }
    else if (action == MenuAction_End)
    {
        delete menu;
    }
    return 0;
}

void SetLinePoint(int client, int set_idx, int point_type)
{
    if (!IsValidClient(client, true))
    {
        Shavit_PrintToChat(client, "%s[Lines] You must be alive to set points.", gS_ChatStrings.sWarning);
        return;
    }
    if (set_idx < 0 || set_idx >= MAX_LINE_SETS) return;
    if (point_type != POINT_A && point_type != POINT_B) return;

    float vStartPos[3], vAngles[3], vEndPos[3];
    GetClientEyePosition(client, vStartPos);
    GetClientEyeAngles(client, vAngles);

    Handle trace = TR_TraceRayFilterEx(vStartPos, vAngles, MASK_SHOT, RayType_Infinite, TraceFilter_NoPlayers, client);

    if (TR_DidHit(trace))
    {
        TR_GetEndPosition(vEndPos, trace);
        g_Points[client][set_idx][point_type]   = vEndPos;
        g_PointSet[client][set_idx][point_type] = true;
        // Shavit_PrintToChat(client, "%s[Lines] Point %s for Line Set %d set at (%.1f, %.1f, %.1f).", gS_ChatStrings.sText,
        //                    point_type == POINT_A ? "A" : "B", set_idx + 1, vEndPos[0], vEndPos[1], vEndPos[2]);
    }
    else {
        Shavit_PrintToChat(client, "%s[Lines] No surface found in front of you.", gS_ChatStrings.sWarning);
    }
    CloseHandle(trace);
}

public bool TraceFilter_NoPlayers(int entity, int contentsMask, any data_client_ignored)
{
    if (entity > 0 && entity <= MaxClients)
    {
        return false;    // Don't hit players
    }
    return true;    // Hit everything else (world, props)
}

void ShowColorMenu(int client)
{
    if (!IsValidClient(client, false)) return;

    Menu menu = new Menu(MenuHandler_Color);
    menu.SetTitle("Select Beam Color");

    for (int i = 0; i < C_NUM_COLOR_OPTIONS; i++)
    {
        menu.AddItem(FormatInt(i), g_sBeamColorNames[i]);
    }
    menu.ExitBackButton = true;
    menu.Display(client, MENU_TIME_FOREVER);
}

public int MenuHandler_Color(Menu menu, MenuAction action, int client, int param2_item)
{
    if (!IsValidClient(client, false) && action != MenuAction_End && action != MenuAction_Cancel)
    {
        delete menu;
        return 0;
    }

    if (action == MenuAction_Select)
    {
        char sInfo[32];
        menu.GetItem(param2_item, sInfo, sizeof(sInfo));

        int color_idx = StringToInt(sInfo);
        if (color_idx >= 0 && color_idx < C_NUM_COLOR_OPTIONS)
        {
            g_PlayerBeamColor[client] = g_BeamColorOptions[color_idx];
            Shavit_PrintToChat(client, "%s[Lines] Beam color set to %s.", gS_ChatStrings.sText, g_sBeamColorNames[color_idx]);
            ShowColorMenu(client);
        }
    }
    else if (action == MenuAction_Cancel)
    {
        int reason = param2_item;    // param2 is MenuCancelReason
        if (reason == MenuCancel_ExitBack)
        {
            if (IsValidClient(client, false))    // Ensure client is still valid
                ShowMainMenu(client);
        }
    }
    else if (action == MenuAction_End) {
        delete menu;
    }
    return 0;
}

void ShowWidthMenu(int client)
{
    if (!IsValidClient(client, false)) return;

    Menu menu = new Menu(MenuHandler_Width);
    menu.SetTitle("Select Beam Width");

    char sBuffer[32];
    for (int i = 0; i < C_NUM_WIDTH_OPTIONS; i++)
    {
        FormatEx(sBuffer, sizeof(sBuffer), "Width: %.1f", g_BeamWidthOptions[i]);
        menu.AddItem(FormatFloat(g_BeamWidthOptions[i]), sBuffer);
    }
    menu.ExitBackButton = true;
    menu.Display(client, MENU_TIME_FOREVER);
}

public int MenuHandler_Width(Menu menu, MenuAction action, int client, int param2_item)
{
    if (!IsValidClient(client, false) && action != MenuAction_End && action != MenuAction_Cancel)
    {
        delete menu;
        return 0;
    }

    if (action == MenuAction_Select)
    {
        char sInfo[32];
        menu.GetItem(param2_item, sInfo, sizeof(sInfo));

        float width        = StringToFloat(sInfo);
        bool  isValidWidth = false;
        for (int i = 0; i < C_NUM_WIDTH_OPTIONS; i++)
        {
            if (FloatAbs(width - g_BeamWidthOptions[i]) < 0.01)
            {    // Compare floats with tolerance
                isValidWidth = true;
                break;
            }
        }
        if (isValidWidth)
        {
            g_PlayerBeamWidth[client] = width;
            Shavit_PrintToChat(client, "%s[Lines] Beam width set to %.1f.", gS_ChatStrings.sText, width);
            ShowWidthMenu(client);
        }
    }
    else if (action == MenuAction_Cancel)
    {
        int reason = param2_item;    // param2 is MenuCancelReason
        if (reason == MenuCancel_ExitBack)
        {
            if (IsValidClient(client, false))    // Ensure client is still valid
                ShowMainMenu(client);
        }
    }
    else if (action == MenuAction_End) {
        delete menu;
    }
    return 0;
}

void ShowVisibilityMenu(int client)
{
    if (!IsValidClient(client, false)) return;

    Menu menu = new Menu(MenuHandler_Visibility);
    menu.SetTitle("Select Line Visibility");

    char currentSelf[32]     = "";
    char currentEveryone[32] = "";
    char buffer[64];

    if (g_PlayerLineVisibility[client] == LineVisibility_Self)
    {
        strcopy(currentSelf, sizeof(currentSelf), " (Current)");
    }
    else
    {
        strcopy(currentEveryone, sizeof(currentEveryone), " (Current)");
    }

    FormatEx(buffer, sizeof(buffer), "Self Only%s", currentSelf);
    menu.AddItem("self", buffer);

    FormatEx(buffer, sizeof(buffer), "Everyone%s", currentEveryone);
    menu.AddItem("everyone", buffer);

    menu.ExitBackButton = true;
    menu.Display(client, MENU_TIME_FOREVER);
}

public int MenuHandler_Visibility(Menu menu, MenuAction action, int client, int param2_item)
{
    if (!IsValidClient(client, false) && action != MenuAction_End && action != MenuAction_Cancel)
    {
        delete menu;
        return 0;
    }

    if (action == MenuAction_Select)
    {
        char sInfo[32];
        menu.GetItem(param2_item, sInfo, sizeof(sInfo));

        if (StrEqual(sInfo, "self"))
        {
            g_PlayerLineVisibility[client] = LineVisibility_Self;
            Shavit_PrintToChat(client, "%s[Lines] Visibility set to Self Only.", gS_ChatStrings.sText);
            ShowVisibilityMenu(client);
        }
        else if (StrEqual(sInfo, "everyone"))
        {
            g_PlayerLineVisibility[client] = LineVisibility_Everyone;
            Shavit_PrintToChat(client, "%s[Lines] Visibility set to Everyone.", gS_ChatStrings.sText);
            ShowVisibilityMenu(client);
        }
    }
    else if (action == MenuAction_Cancel)
    {
        int reason = param2_item;    // param2 is MenuCancelReason
        if (reason == MenuCancel_ExitBack)
        {
            if (IsValidClient(client, false))    // Ensure client is still valid
                ShowMainMenu(client);
        }
    }
    else if (action == MenuAction_End) {
        {
            delete menu;    // This extra block is harmless but unnecessary
        }
    }
    return 0;    // Ensure the function always returns an integer value
}
// The Timer_DrawBeams function definition should be outside of the menu handler
public Action Timer_DrawBeams(Handle timer)
{
    if (g_LaserSprite == 0) return Plugin_Continue;    // Sprite not loaded

    for (int line_owner_idx = 1; line_owner_idx <= MaxClients; line_owner_idx++)
    {
        if (!IsValidClient(line_owner_idx, false)) continue;

        for (int set_idx = 0; set_idx < MAX_LINE_SETS; set_idx++)
        {
            if (g_PointSet[line_owner_idx][set_idx][POINT_A] && g_PointSet[line_owner_idx][set_idx][POINT_B])

            {
                // Check if points are identical (zero-length beam), which might be invisible or cause issues.
                if (g_Points[line_owner_idx][set_idx][POINT_A][0] == g_Points[line_owner_idx][set_idx][POINT_B][0] && g_Points[line_owner_idx][set_idx][POINT_A][1] == g_Points[line_owner_idx][set_idx][POINT_B][1] && g_Points[line_owner_idx][set_idx][POINT_A][2] == g_Points[line_owner_idx][set_idx][POINT_B][2])
                {
                    // LogMessage("[Lines] Skipping beam for owner %d, set %d: Points A and B are identical.", line_owner_idx, set_idx);
                    continue;
                }

                // Beam life: make it slightly longer than the timer interval to ensure continuous visibility.
                float beam_life = LINE_TIMER_INTERVAL + 0.1;

                TE_SetupBeamPoints(g_Points[line_owner_idx][set_idx][POINT_A],
                                   g_Points[line_owner_idx][set_idx][POINT_B],
                                   g_LaserSprite,
                                   0,
                                   0,
                                   0,
                                   beam_life,
                                   g_PlayerBeamWidth[line_owner_idx],
                                   g_PlayerBeamWidth[line_owner_idx],
                                   0,
                                   0.0,
                                   g_PlayerBeamColor[line_owner_idx],
                                   0);

                if (g_PlayerLineVisibility[line_owner_idx] == LineVisibility_Everyone)
                {
                    TE_SendToAll();
                }
                else    // LineVisibility_Self
                {
                    int recipient[1];
                    recipient[0] = line_owner_idx;
                    TE_Send(recipient, 1, 0.0);
                }
            }
        }
    }
    return Plugin_Continue;
}

char g_sFormatIntBuffer[12];    // Buffer for FormatInt
stock char[] FormatInt(int num)
{
    IntToString(num, g_sFormatIntBuffer, sizeof(g_sFormatIntBuffer));
    return g_sFormatIntBuffer;
}

char g_sFormatFloatBuffer[32];    // Buffer for FormatFloat
stock char[] FormatFloat(float num)
{
    FloatToString(num, g_sFormatFloatBuffer, sizeof(g_sFormatFloatBuffer));
    return g_sFormatFloatBuffer;
}