#include <sourcemod>
#include <sdkhooks>
#include <shavit>

#pragma newdecls required
#pragma semicolon 1

public Plugin myinfo =
{
    name        = "S4F - Noclip ignore triggers",
    author      = "Proxychains ayaya",
    description = "Allow players to ignore triggers in noclip while holding space (+jump)",
    version     = "1.0",
    url         = "https://github.com/surfing4fun"
};

// Forward to define IN_JUMP if not already, though sourcemod.inc should provide it.
// #define IN_JUMP			(1 << 1)
public void OnEntityCreated(int entity, const char[] classname)
{
    if (
        StrEqual(classname, "trigger_apply_impulse")
        || StrEqual(classname, "trigger_capture_area")
        || StrEqual(classname, "trigger_catapult")
        || StrEqual(classname, "trigger_hurt")
        || StrEqual(classname, "trigger_impact")
        || StrEqual(classname, "trigger_teleport_relative")
        || StrEqual(classname, "trigger_multiple")
        || StrEqual(classname, "trigger_once")
        || StrEqual(classname, "trigger_push")
        || StrEqual(classname, "trigger_teleport")
        || StrEqual(classname, "trigger_gravity"))
    {
        SDKHook(entity, SDKHook_StartTouch, HookTrigger);
        SDKHook(entity, SDKHook_EndTouch, HookTrigger);
        SDKHook(entity, SDKHook_Touch, HookTrigger);
    }
}

public Action HookTrigger(int entity, int other)
{
    if (IsValidClient(other))
    {
        // Check if player is in noclip
        bool isInNoclip = (GetEntityMoveType(other) == MOVETYPE_NOCLIP);

        if (isInNoclip)
        {
            // Check if player is holding jump (spacebar)
            bool isHoldingJump = (GetClientButtons(other) & IN_JUMP) != 0;
            if (isHoldingJump)
            {
                return Plugin_Handled;    // Ignore the trigger
            }
        }
    }
    return Plugin_Continue;
}