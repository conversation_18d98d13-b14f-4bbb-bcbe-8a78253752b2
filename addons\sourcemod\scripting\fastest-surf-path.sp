// fastest path between two set points
#pragma semicolon 1
#pragma newdecls required

#include <sourcemod>
#include <sdktools>
#include <sdktools_tempents>    // For TE_SendToFilter, TE_SendToClient, etc.
// sdktools_tempents_stocks is no longer used as CreateRecipientFilter is no longer needed.

#define MAX_POINTS       2
#define GRAVITY          800.0
#define CURVE_RESOLUTION 100    // Number of segments in the curve

// Globals for points A and B
float g_SurfPoints[MAXPLAYERS + 1][MAX_POINTS][3];
bool  g_SelectedPoints[MAXPLAYERS + 1][MAX_POINTS];
float g_SurfPointNormals[MAXPLAYERS + 1][MAX_POINTS][3];    // Store surface normal for each point

// Globals for path calculation and visualization
float g_CurvePath[MAXPLAYERS + 1][CURVE_RESOLUTION + 1][3];    // Stores 3D points of the calculated path
int   g_CurvePathLength[MAXPLAYERS + 1];                       // Number of points in g_CurvePath
bool  g_PathCalculated[MAXPLAYERS + 1];                        // Flag if path has been calculated
int   g_BeamColor[4] = { 100, 150, 255, 200 };                 // R, G, B, A for the path beam (light blue, semi-transparent)

// Engine constants
float g_AirAccelerate;
float g_TickInterval;    // Note: Current plugin sets this to TicksPerSecond. Physics might be off.

// Visuals
int   g_GlowSprite;
int   g_LaserSprite;               // For drawing the path
bool  g_ShowGlowSprites = true;    // Renamed from sm_show_glowsprites cvar for clarity
public Plugin myinfo =
{
    name        = "S4F - Fastest Surf Path Visualizer",
    author      = "Original by followingthefasciaplane, Modified for S4F",
    description = "Calculates and visualizes the fastest surf path between two points with lasers.",
    url         = "https://github.com/followingthefasciaplane/sm-misc/blob/main/fastest-surf-path.sp",
    version     = "1.2"
};

public void OnPluginStart()
{
    RegAdminCmd("sm_surfmenu", Command_SurfMenu, ADMFLAG_ROOT, "Opens the surf path calculator menu");
    HookEvent("player_spawn", Event_PlayerSpawn, EventHookMode_Post);
    g_AirAccelerate = GetConVarFloat(FindConVar("sv_airaccelerate"));
    g_TickInterval  = 1.0 / GetTickInterval();    // This results in TicksPerSecond. Standard physics would use GetTickInterval() (SecondsPerTick).

    g_GlowSprite    = PrecacheModel("materials/sprites/blueglow1.vmt");    // Existing
    g_LaserSprite   = PrecacheModel("shavit/laserbeam.vmt");               // For path visualization, using shavit's sprite

    CreateConVar("sm_surfpath_show_visuals", "1", "Toggle display of glow sprites for points and laser path.", FCVAR_NOTIFY, true, 0.0, true, 1.0);
    HookConVarChange(FindConVar("sm_surfpath_show_visuals"), ConVarChange_ShowVisuals);
    g_ShowGlowSprites = GetConVarBool(FindConVar("sm_surfpath_show_visuals"));    // Initialize based on cvar

    CreateTimer(0.1, Timer_DrawVisuals, _, TIMER_REPEAT | TIMER_FLAG_NO_MAPCHANGE);

    for (int i = 1; i <= MaxClients; i++)
    {
        ResetSelectedPoints(i);    // Resets points, normals, and path data
    }
}

public void OnMapStart()
{
    // Re-precache models on map start
    g_GlowSprite  = PrecacheModel("materials/sprites/blueglow1.vmt");
    g_LaserSprite = PrecacheModel("shavit/laserbeam.vmt");    // Use shavit's sprite
    // Timer is already set with NO_MAPCHANGE, but if it were to be stopped, restart here.
}

public void ConVarChange_ShowVisuals(ConVar cvar, const char[] oldValue, const char[] newValue)
{
    g_ShowGlowSprites = StringToInt(newValue) == 1;
}

public Action Command_SurfMenu(int client, int args)
{
    if (!IsValidClient(client))
    {
        return Plugin_Handled;
    }
    ShowSurfMenu(client);
    return Plugin_Handled;
}

void ShowSurfMenu(int client)
{
    Menu menu = new Menu(MenuHandler_SurfMenu);
    menu.SetTitle("Surf Path Calculator");

    char itemText[64];
    FormatEx(itemText, sizeof(itemText), "Set Point A %s", g_SelectedPoints[client][0] ? "(Selected)" : "");
    menu.AddItem("point_a", itemText, g_SelectedPoints[client][0] ? ITEMDRAW_DISABLED : ITEMDRAW_DEFAULT);

    FormatEx(itemText, sizeof(itemText), "Set Point B %s", g_SelectedPoints[client][1] ? "(Selected)" : "");
    menu.AddItem("point_b", itemText, g_SelectedPoints[client][1] ? ITEMDRAW_DISABLED : ITEMDRAW_DEFAULT);

    if (g_SelectedPoints[client][0] && g_SelectedPoints[client][1])
    {
        menu.AddItem("calculate", "Calculate Fastest Path");
    }
    menu.AddItem("reset", "Reset Points & Path");
    menu.ExitButton = true;
    menu.Display(client, MENU_TIME_FOREVER);
}

public int MenuHandler_SurfMenu(Menu menu, MenuAction action, int client, int itemNum)
{
    if (action == MenuAction_Select)
    {
        char info[32];
        menu.GetItem(itemNum, info, sizeof(info));

        if (StrEqual(info, "point_a")) SetPoint(client, 0);
        else if (StrEqual(info, "point_b")) SetPoint(client, 1);
        else if (StrEqual(info, "calculate")) CalculateFastestPath(client);
        else if (StrEqual(info, "reset")) {
            ResetSelectedPoints(client);    // This also resets path data
            PrintToChat(client, "Points and path visualization reset.");
        }

        // Re-display menu for options that change its state (Set Point, Reset)
        // "Calculate" will print its own message and the menu can close.
        if (StrEqual(info, "point_a") || StrEqual(info, "point_b") || StrEqual(info, "reset"))
        {
            ShowSurfMenu(client);    // Recreate and show the menu to reflect updated states
        }
    }
    else if (action == MenuAction_End)
    {
        delete menu;
    }
    return 0;
}

void SetPoint(int client, int index)
{
    float startPos[3], angles[3], endPos[3];
    GetClientEyePosition(client, startPos);
    GetClientEyeAngles(client, angles);

    // Use MASK_SHOT for setting points.
    // MASK_SHOT includes CONTENTS_HITBOX, which is generally better for props than MASK_SOLID.
    Handle trace = TR_TraceRayFilterEx(startPos, angles, MASK_SHOT, RayType_Infinite, TraceFilter_AllSolid, client);
    if (TR_DidHit(trace))
    {
        TR_GetPlaneNormal(trace, g_SurfPointNormals[client][index]);    // Store the surface normal
        TR_GetEndPosition(endPos, trace);
        g_SurfPoints[client][index][0]  = endPos[0];
        g_SurfPoints[client][index][1]  = endPos[1];
        g_SurfPoints[client][index][2]  = endPos[2];
        g_SelectedPoints[client][index] = true;
        PrintToChat(client, "Point %c set at (%.2f, %.2f, %.2f)", index == 0 ? 'A' : 'B', endPos[0], endPos[1], endPos[2]);
        // If points are changed, invalidate the old path
        g_PathCalculated[client]  = false;
        g_CurvePathLength[client] = 0;
    }
    else
    {
        PrintToChat(client, "No valid surface found under the crosshair.");
    }
    CloseHandle(trace);
}

// Trace filter that hits any entity matching the contentsMask
// Must accept 'any data' even if not used, to match TR_TraceRayFilter's expected signature.
public bool TraceFilter_HitAll(int entity, int contentsMask, any data)
{
    return true;    // We want to hit any solid entity.
}

// Custom trace filter to ignore the calling client
public bool TraceFilter_AllSolid(int entity, int contentsMask, any data_client)
{
    return entity != view_as<int>(data_client);
}

void CalculateFastestPath(int client)
{
    // Invalidate previous path calculation
    g_PathCalculated[client]  = false;
    g_CurvePathLength[client] = 0;

    if (!g_SelectedPoints[client][0] || !g_SelectedPoints[client][1])
    {
        PrintToChat(client, "Please set both Point A and Point B first.");
        return;
    }

    float vStart[3], vEnd[3], vPlayerVelocity[3], vNormal[3];
    vStart = g_SurfPoints[client][0];
    vEnd   = g_SurfPoints[client][1];
    GetEntPropVector(client, Prop_Data, "m_vecVelocity", vPlayerVelocity);    // Player's current velocity
    vNormal = g_SurfPointNormals[client][0];                                  // Use the stored normal of the surface at Point A

    // Check for (near) vertical alignment first
    float horiz_dist_vec[3];
    horiz_dist_vec[0] = vEnd[0] - vStart[0];
    horiz_dist_vec[1] = vEnd[1] - vStart[1];
    horiz_dist_vec[2] = 0.0;
    if (GetVectorLength(horiz_dist_vec) < 0.01)    // Essentially vertically aligned
    {
        g_CurvePath[client][0]    = vStart;
        g_CurvePath[client][1]    = vEnd;
        g_CurvePathLength[client] = 2;
        g_PathCalculated[client]  = true;
        PrintToChat(client, "Path is (near) vertical. Displaying straight line. Time calculation might be inaccurate.");
        return;
    }

    // The trace between points A and B to find a surface normal is removed.
    // We now use the normal of the surface at Point A, stored when Point A was set.

    float fRampAngle = CalculateRampAngle(vNormal);
    float clippedVelocity[3];
    ClipVelocity(vPlayerVelocity, vNormal, clippedVelocity);    // Clip player's current velocity against Point A's surface normal

    // Store vStart as the first point of the path
    g_CurvePath[client][0]    = vStart;
    g_CurvePathLength[client] = 1;

    // --- Begin path calculation logic ---
    // Calculate total horizontal distance for scaling and progress
    float temp_horiz_vec[3];
    temp_horiz_vec[0]              = vEnd[0] - vStart[0];
    temp_horiz_vec[1]              = vEnd[1] - vStart[1];
    temp_horiz_vec[2]              = 0.0;
    float fTotalHorizontalDistance = GetVectorLength(temp_horiz_vec);

    // Safeguard if fTotalHorizontalDistance is effectively zero (should be caught by earlier vertical check)
    if (fTotalHorizontalDistance < 0.001)
    {
        g_CurvePath[client][1]    = vEnd;    // Just connect to B
        g_CurvePathLength[client] = 2;
        g_PathCalculated[client]  = true;
        PrintToChat(client, "Path is (near) vertical (secondary check). Displaying straight line.");
        return;
    }

    float fScalingFactor       = fTotalHorizontalDistance / (2.0 * FLOAT_PI);    // r = D_h / (2*PI) for one cycloid arch
    float fThetaMax            = 360.0;                                          // Degrees, for one full cycloid arch (0 to 2*PI)
    float fFastestTime         = 0.0;
    float fDeltaTheta          = fThetaMax / CURVE_RESOLUTION;
    float fPrevX_local_cycloid = 0.0;                                              // Local X along the cycloid's own generation axis
    float fPrevY_local_cycloid = 0.0;                                              // Local Y (drop) for the cycloid
    float fPrevVelocity        = GetVectorLength(clippedVelocity);                 // Use initial speed from clipped velocity
    float fSurfGravity         = GRAVITY * GetSurfaceGravityFactor(fRampAngle);    // Effective gravity along ramp plane

    float horiz_dir[3];    // Direction vector for horizontal component of path
    // Normalize temp_horiz_vec to get horiz_dir
    horiz_dir[0] = temp_horiz_vec[0] / fTotalHorizontalDistance;
    horiz_dir[1] = temp_horiz_vec[1] / fTotalHorizontalDistance;
    horiz_dir[2] = 0.0;

    for (int i = 1; i <= CURVE_RESOLUTION; i++)
    {
        float fTheta               = i * fDeltaTheta;    // Current angle for cycloid parameter (degrees)

        // Calculate cycloid's own X displacement and Y drop component
        float fX_cycloid_component = fScalingFactor * ((fTheta * FLOAT_PI / 180.0) - SineD(fTheta));
        float fY_drop_component    = fScalingFactor * (1.0 - CosineD(fTheta));    // This is the "dip"

        // Arc length of the current cycloid segment for time calculation
        float fDistance_segment    = SquareRoot(Pow(fX_cycloid_component - fPrevX_local_cycloid, 2.0) + Pow(fY_drop_component - fPrevY_local_cycloid, 2.0));

        float fAcceleration        = g_AirAccelerate * g_TickInterval;
        float fGravityComponent    = fSurfGravity * g_TickInterval;
        float fTime                = CalculateSegmentTime(fDistance_segment, fPrevVelocity, fAcceleration, fGravityComponent);

        fFastestTime += fTime;
        fPrevX_local_cycloid = fX_cycloid_component;
        fPrevY_local_cycloid = fY_drop_component;
        fPrevVelocity        = CalculateSegmentEndVelocity(fPrevVelocity, fAcceleration, fGravityComponent, fTime);

        // Store the 3D point for visualization
        float current_3d_point[3];
        float progress = fX_cycloid_component / fTotalHorizontalDistance;           // Progress along horizontal line from A to B
        if (progress > 1.0) progress = 1.0;                                         // Clamp progress
        float interpolatedZ = vStart[2] * (1.0 - progress) + vEnd[2] * progress;    // Z on straight line from A to B
        current_3d_point[0] = vStart[0] + horiz_dir[0] * fX_cycloid_component;
        current_3d_point[1] = vStart[1] + horiz_dir[1] * fX_cycloid_component;
        current_3d_point[2] = interpolatedZ - fY_drop_component;    // Apply cycloid dip below the interpolated Z

        // Ensure the very last point is exactly vEnd before snapping
        if (i == CURVE_RESOLUTION)
        {
            current_3d_point = vEnd;
        }

        g_CurvePath[client][i] = current_3d_point;
        g_CurvePathLength[client]++;
    }
    // --- End path calculation logic ---

    // --- Begin Surface Snapping Logic ---
    // This will adjust the Z-coordinate of each path point to conform to the underlying surface.
    // Only snap if a path was actually generated by the cycloid math above.
    if (g_CurvePathLength[client] > 1)
    {
        // Determine a robust Z range for snapping traces based on actual start/end points
        float max_z_for_snapping_trace = MaxFloat(g_SurfPoints[client][0][2], g_SurfPoints[client][1][2]) + 128.0;    // Start trace well above highest point
        float min_z_for_snapping_trace = MinFloat(g_SurfPoints[client][0][2], g_SurfPoints[client][1][2]) - 128.0;    // End trace well below lowest point

        for (int i = 0; i < g_CurvePathLength[client]; i++)
        {
            float current_xy_from_ideal_path[3];
            current_xy_from_ideal_path = g_CurvePath[client][i];    // Get the X,Y from the ideal path calculation

            float trace_start[3], trace_end[3];

            trace_start[0] = current_xy_from_ideal_path[0];
            trace_start[1] = current_xy_from_ideal_path[1];
            trace_start[2] = max_z_for_snapping_trace;    // Start trace from well above the ramp

            trace_end[0]   = current_xy_from_ideal_path[0];
            trace_end[1]   = current_xy_from_ideal_path[1];
            trace_end[2]   = min_z_for_snapping_trace;    // End trace well below the ramp

            // TraceFilter_HitAll ensures we consider any solid entity hit.
            // Using MASK_SHOT for surface snapping, as it includes CONTENTS_HITBOX, good for props.
            // Using TR_TraceRayFilterEx, passing 0 as entity to ignore (TraceFilter_HitAll doesn't use this param).
            Handle trace   = TR_TraceRayFilterEx(trace_start, trace_end, MASK_SHOT, RayType_EndPoint, TraceFilter_HitAll, 0);

            if (TR_DidHit(trace))
            {
                float hit_pos[3];
                TR_GetEndPosition(hit_pos, trace);
                g_CurvePath[client][i][2] = hit_pos[2] + 0.1;    // Update Z to surface height, +0.1 to reduce Z-fighting
            }
            // If the trace does not hit (e.g., path goes over a gap), the Z from the ideal cycloid calculation remains.
            CloseHandle(trace);
        }

        // Ensure the path visually starts and ends *exactly* where the user clicked.
        // Add a tiny Z offset so the beam is visible right on the surface.
        g_CurvePath[client][0] = g_SurfPoints[client][0];
        g_CurvePath[client][0][2] += 0.1;

        if (g_CurvePathLength[client] > 0)
        {    // Check to prevent array out of bounds if length is 0
            g_CurvePath[client][g_CurvePathLength[client] - 1] = g_SurfPoints[client][1];
            g_CurvePath[client][g_CurvePathLength[client] - 1][2] += 0.1;
        }
    }
    // --- End Surface Snapping Logic ---

    g_PathCalculated[client] = true;
    PrintToChat(client, "Fastest time between points: %.2f seconds. Path visualized.", fFastestTime);
}
void ClipVelocity(const float vIn[3], const float vNormal[3], float vOut[3])
{
    float backoff = GetVectorDotProduct(vIn, vNormal);    // Changed from GetVectorDotProductD
    for (int i = 0; i < 3; i++)
    {
        vOut[i] = vIn[i] - (vNormal[i] * backoff);
    }
}

float CalculateRampAngle(const float vNormal[3])
{
    // Ensure normal is normalized for acos
    float normalizedNormal[3];
    normalizedNormal = vNormal;    // Copy
    NormalizeVector(normalizedNormal, normalizedNormal);
    return ArcCosine(normalizedNormal[2]) * (180.0 / FLOAT_PI);    // Angle with Z-axis (up)
}

public void Event_PlayerSpawn(Event event, const char[] name, bool dontBroadcast)
{
    int client = GetClientOfUserId(event.GetInt("userid"));

    if (IsValidClient(client))
    {
        ResetSelectedPoints(client);    // This will also call ResetClientPathData
    }
}

bool IsValidClient(int client)
{
    return (client > 0 && client <= MaxClients && IsClientConnected(client) && IsClientInGame(client));
}

void ResetClientPathData(int client)
{
    g_PathCalculated[client]  = false;
    g_CurvePathLength[client] = 0;
}

void ResetSelectedPoints(int client)
{
    for (int i = 0; i < MAX_POINTS; i++)
    {
        g_SelectedPoints[client][i] = false;
        for (int j = 0; j < 3; j++)
        {
            g_SurfPoints[client][i][j]       = 0.0;
            g_SurfPointNormals[client][i][j] = 0.0;    // Reset normal data
        }
    }

    ResetClientPathData(client);    // Also reset path data
}

float CalculateSegmentTime(float fDistance, float fInitialVelocity, float fAcceleration, float fGravityComponent)
{
    // This function solves d = v0*t + 0.5*A*t^2 for t, where A = fAccelerationFactor + fGravityFactor
    // The factors are dimensionally suspect (see earlier analysis) but kept as per original.
    float totalEffectiveAcceleration = fAcceleration + fGravityComponent;

    if (FloatAbs(totalEffectiveAcceleration) < 0.0001)    // Avoid division by zero if no effective accel/decel
    {
        if (FloatAbs(fInitialVelocity) < 0.0001) return 0.0;    // No velocity, no acceleration -> no time to cover distance (or infinite if dist > 0)

        return fDistance / fInitialVelocity;
    }

    // Quadratic formula: 0.5*A*t^2 + v0*t - d = 0
    // a = 0.5*A, b = v0, c = -d
    // t = (-b +/- sqrt(b^2 - 4ac)) / 2a
    float a            = 0.5 * totalEffectiveAcceleration;
    float b            = fInitialVelocity;
    float c            = -fDistance;

    float discriminant = b * b - 4.0 * a * c;
    if (discriminant < 0.0) return 0.0;    // No real solution (should not happen if moving forward)

    float t1 = (-b + SquareRoot(discriminant)) / (2.0 * a);
    float t2 = (-b - SquareRoot(discriminant)) / (2.0 * a);

    // Return the positive, smallest time, or larger if physically makes sense
    if (t1 >= 0 && t2 >= 0) return MinFloat(t1, t2);    // Typically want smallest positive time
    if (t1 >= 0) return t1;
    if (t2 >= 0) return t2;
    return 0.0;    // No positive time solution
}

float CalculateSegmentEndVelocity(float fInitialVelocity, float fAcceleration, float fGravityComponent, float fTime)
{
    return fInitialVelocity + (fAcceleration + fGravityComponent) * fTime;
}

float GetSurfaceGravityFactor(float fRampAngle)
{
    return Cosine(fRampAngle * (FLOAT_PI / 180.0));    // Cosine expects radians
}

public Action Timer_DrawVisuals(Handle timer)
{
    if (!g_ShowGlowSprites)    // Cvar now controls all visuals from this plugin
    {
        return Plugin_Continue;
    }

    for (int client = 1; client <= MaxClients; client++)
    {
        // Draw Point A and B markers (glow sprites)
        if (IsValidClient(client))
        {
            for (int i = 0; i < MAX_POINTS; i++)
            {
                if (g_SelectedPoints[client][i])
                {
                    TE_SetupGlowSprite(g_SurfPoints[client][i], g_GlowSprite, 0.15, 0.5, 150);    // Life, Size (4x smaller than 15.0), Brightness
                    TE_SendToAll();                                                               // Sends to everyone. Could be filtered to only the client if desired.
                }
            }
            // Draw calculated path if available
            if (g_PathCalculated[client] && g_CurvePathLength[client] > 1)
            {
                for (int j = 0; j < g_CurvePathLength[client] - 1; j++)
                {
                    TE_SetupBeamPoints(g_CurvePath[client][j],        // Start point
                                       g_CurvePath[client][j + 1],    // End point
                                       g_LaserSprite,                 // Model Index
                                       0,                             // Halo Index (0 = no halo)
                                       0,                             // Start Frame
                                       66,                            // FrameRate (mimicking shavit-ghost2)
                                       0.3,                           // Life (timer updates every 0.1s)
                                       1.5,                           // Width
                                       1.5,                           // EndWidth
                                       0,                             // FadeLength (0 = no fade)
                                       0.0,                           // Amplitude (0 = no noise)
                                       g_BeamColor,                   // Color RGBA
                                       0);                            // Speed (texture scroll speed)
                    TE_SendToClient(client);                          // Send to the specific client
                }
            }
        }
    }

    return Plugin_Continue;
}

// Math utility functions (degrees)
float SineD(float x)
{
    return Sine(x * FLOAT_PI / 180.0);
}

float CosineD(float x)
{
    return Cosine(x * FLOAT_PI / 180.0);
}
// float ArcTangent2D(float y, float x) { return ArcTangent2(y,x) * (180.0 / FLOAT_PI); } // Unused now
float MaxFloat(float a, float b) { return (a > b) ? a : b; }
float MinFloat(float a, float b) { return (a < b) ? a : b; }
// float GetVectorLengthD(const float vec[3]) { return SquareRoot(vec[0]*vec[0] + vec[1]*vec[1] + vec[2]*vec[2]); } // Use GetVectorLength
// float GetVectorDotProductD(const float vec1[3], const float vec2[3]) { return vec1[0]*vec2[0] + vec1[1]*vec2[1] + vec1[2]*vec2[2]; } // Use GetVectorDotProduct
