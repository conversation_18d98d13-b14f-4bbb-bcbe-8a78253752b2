#include <sdkhooks>
#include <sdktools>
#include <discord>
#include <multicolors>
#include <autoexecconfig>
#undef REQUIRE_EXTENSIONS
#include <ripext>

#pragma semicolon 1
#pragma newdecls required

#include "discordrelay/convars.sp"
#include "discordrelay/globals.sp"
#include "discordrelay/calladmin.sp"
#include "discordrelay/wordfilter.sp"

#define PLUGIN_VERSION "1.4.6"

public Plugin myinfo =
{
    name        = "[ANY] Discord Relay",
    author      = "Heapons (forked from log-ical and maxijabase)",
    description = "Discord ⇄ Server Relay",
    version     = PLUGIN_VERSION,
    url         = "https://github.com/Heapons/sp-discordrelay"


}

public APLRes
    AskPluginLoad2(Handle myself, bool late, char[] error, int err_max)
{
    g_Late = late;
    return APLRes_Success;
}

public void OnPluginStart()
{
    LoadTranslations("discordrelay.phrases.txt");
    SetupConvars();

    WordFilter_OnPluginStart();

    g_ChatAnnounced     = false;
    g_RCONAnnounced     = false;
    g_bConVarsLoaded    = false;
    g_iConVarRetryCount = 0;

    // Initial ConVar loading with delay to ensure config is fully loaded
    CreateTimer(1.0, Timer_InitialConVarLoad, _, TIMER_FLAG_NO_MAPCHANGE);

    if (g_Late)
    {
        for (int i = 1; i <= MaxClients; i++)
        {
            if (IsClientInGame(i))
            {
                OnClientPostAdminCheck(i);
            }
        }
    }

    RegConsoleCmd("sm_calladmin", Command_CallAdmin, "Call an admin via Discord relay");
    RegConsoleCmd("sm_bug", Command_CallAdmin, "Call an admin via Discord relay");
    RegConsoleCmd("sm_bugreport", Command_CallAdmin, "Call an admin via Discord relay");
    RegAdminCmd("sm_discordrelay_status", Command_DiscordRelayStatus, ADMFLAG_ROOT, "Check the status of Discord Relay ConVars");
}

public Action Timer_InitialConVarLoad(Handle timer)
{
    // Load all ConVars
    LoadAllConVars();

    // Verify critical ConVars
    if (VerifyCriticalConVars())
    {
        PrintToServer("[Discord Relay] ConVars successfully loaded on initial load");
        g_bConVarsLoaded = true;

        // Initialize Discord functionality
        InitializeDiscordFunctionality();

        // If late-loaded, send server start message
        if (g_Late && g_cvMapChangeMessage.BoolValue)
        {
            CreateTimer(1.0, Timer_ServerStart);
        }
    }
    else
    {
        // Start retry mechanism
        g_iConVarRetryCount = 0;
        g_hConVarRetryTimer = CreateTimer(RETRY_DELAY, Timer_RetryConVarLoading, _, TIMER_FLAG_NO_MAPCHANGE);
    }

    return Plugin_Stop;
}

public void OnClientPostAdminCheck(int client)
{
    if (!IsValidClient(client))
    {
        return;
    }

    Player player;
    player.Load(client);
}

public void OnClientDisconnect(int client)
{
    if (!IsValidClient(client))
    {
        return;
    }
    int userid = GetClientUserId(client);
    if (g_cvDisconnectMessage.BoolValue)
    {
        char phrase[128];
        char name[64];
        Format(name, sizeof(name), "%N", client);
        Format(phrase, sizeof(phrase), "%T", "Player Leave", LANG_SERVER, name, g_Players[userid].SteamID64);
        PrintToDiscord(userid, g_sDisconnectMessageColor, phrase);
    }

    Player newPlayer;
    g_Players[userid] = newPlayer;
}

public Action OnBanClient(int client)
{
    if (!IsValidClient(client))
    {
        return;
    }
    int userid = GetClientUserId(client);
    if (g_cvDisconnectMessage.BoolValue)
    {
        char phrase[128];
        char name[64];
        Format(name, sizeof(name), "%N", client);
        Format(phrase, sizeof(phrase), "%T", "Player Banned", LANG_SERVER, name, g_Players[userid].SteamID64);
        PrintToDiscord(userid, g_sBanMessageColor, phrase);
    }

    Player newPlayer;
    g_Players[userid] = newPlayer;
}

public void OnMapStart()
{
    // Reset ConVar loading state for new map
    g_bConVarsLoaded    = false;
    g_iConVarRetryCount = 0;

    // Cancel any existing retry timer
    if (g_hConVarRetryTimer != null)
    {
        KillTimer(g_hConVarRetryTimer);
        g_hConVarRetryTimer = null;
    }

    // Skip if this is a late load (handled in OnPluginStart)
    if (g_Late)
    {
        g_Late = false;
        return;
    }

    // Load ConVars with a slight delay to ensure configs are reloaded
    CreateTimer(2.0, Timer_MapStartConVarLoad, _, TIMER_FLAG_NO_MAPCHANGE);
}

public Action Timer_MapStartConVarLoad(Handle timer)
{
    // Load all ConVars
    LoadAllConVars();

    // Verify critical ConVars
    if (VerifyCriticalConVars())
    {
        PrintToServer("[Discord Relay] ConVars successfully loaded after map change");
        g_bConVarsLoaded = true;

        // Initialize Discord functionality
        InitializeDiscordFunctionality();
    }
    else
    {
        // Start retry mechanism
        g_iConVarRetryCount = 0;
        g_hConVarRetryTimer = CreateTimer(RETRY_DELAY, Timer_RetryConVarLoading, _, TIMER_FLAG_NO_MAPCHANGE);
    }

    return Plugin_Stop;
}

public void OnMapEnd()
{
    // Cancel any existing retry timer
    if (g_hConVarRetryTimer != null)
    {
        KillTimer(g_hConVarRetryTimer);
        g_hConVarRetryTimer = null;
    }

    // Only proceed with Discord operations if ConVars are loaded
    if (!g_bConVarsLoaded)
    {
        return;
    }

    if (g_cvListenAnnounce.BoolValue)
    {
        if (g_ChatAnnounced)
        {
            char phrase[64];
            Format(phrase, sizeof(phrase), "%T", "Chat Relay Stopped", LANG_SERVER);
            // Use specific webhook if set, otherwise fallback to main webhook
            char webhook[256];
            if (g_sListenChatWebhook[0])
                strcopy(webhook, sizeof(webhook), g_sListenChatWebhook);
            else
                strcopy(webhook, sizeof(webhook), g_sDiscordWebhook);
            PrintToChannel(webhook, phrase, g_sListenAnnounceColor);
        }

        if (g_RCONAnnounced)
        {
            char phrase[64];
            Format(phrase, sizeof(phrase), "%T", "RCON Relay Stopped", LANG_SERVER);
            // Use specific webhook if set, otherwise fallback to main webhook
            char webhook[256];
            if (g_sListenRCONWebhook[0])
                strcopy(webhook, sizeof(webhook), g_sListenRCONWebhook);
            else
                strcopy(webhook, sizeof(webhook), g_sRCONWebhook);
            PrintToChannel(webhook, phrase, g_sListenAnnounceColor);
        }
    }

    if (g_Bot)
    {
        if (g_Bot.IsListeningToChannelID(g_sChannelId)) g_Bot.StopListeningToChannelID(g_sChannelId);
        if (g_Bot.IsListeningToChannelID(g_sRCONChannelId)) g_Bot.StopListeningToChannelID(g_sRCONChannelId);

        MapEnd();
        delete g_Bot;
        g_Bot = null;
    }
}

public void OnServerEnterHibernation()
{
    if (!g_cvServerHibernation.BoolValue) return;

    char phrase[128];
    Format(phrase, sizeof(phrase), "%T", "Hibernation Enter", LANG_SERVER);

    // Use specific webhook if set, otherwise fallback to main webhook
    char webhook[256];
    if (g_sServerHibernationWebhook[0])
        strcopy(webhook, sizeof(webhook), g_sServerHibernationWebhook);
    else
        strcopy(webhook, sizeof(webhook), g_sDiscordWebhook);
    AnnounceToChannel(webhook, phrase, g_sServerHibernationEnterColor);
}

public void OnServerExitHibernation()
{
    if (!g_cvServerHibernation.BoolValue) return;

    char phrase[128];
    Format(phrase, sizeof(phrase), "%T", "Hibernation Exit", LANG_SERVER);

    // Use specific webhook if set, otherwise fallback to main webhook
    char webhook[256];
    if (g_sServerHibernationWebhook[0])
        strcopy(webhook, sizeof(webhook), g_sServerHibernationWebhook);
    else
        strcopy(webhook, sizeof(webhook), g_sDiscordWebhook);
    AnnounceToChannel(webhook, phrase, g_sServerHibernationExitColor);
}

public Action Timer_CreateBot(Handle timer)
{
    // Only proceed if ConVars are loaded
    if (!g_bConVarsLoaded)
    {
        LogError("[Discord Relay] Attempted to create bot before ConVars were loaded");
        return Plugin_Handled;
    }

    if (!g_sDiscordBotToken[0])
    {
        LogError("Bot token not configured!");
        return Plugin_Handled;
    }

    if (g_Bot)
    {
        PrintToServer("Bot already configured, skipping...");
        return Plugin_Handled;
    }

    g_Bot = new DiscordBot(g_sDiscordBotToken);
    CreateTimer(1.0, Timer_GetGuildList, _, TIMER_FLAG_NO_MAPCHANGE);
    return Plugin_Continue;
}

public Action Timer_ServerStart(Handle timer)
{
    char buffer[64];
    GetCurrentMap(buffer, sizeof(buffer));
    PrintToDiscordMapChange(buffer, g_sServerStartColor);
    return Plugin_Continue;
}

public Action Timer_MapStart(Handle timer)
{
    char buffer[64];
    GetCurrentMap(buffer, sizeof(buffer));
    PrintToDiscordMapChange(buffer, g_sCurrentMapColor);
    return Plugin_Continue;
}

public Action MapEnd()
{
    char buffer[64];
    GetCurrentMap(buffer, sizeof(buffer));
    PrintToDiscordPreviousMap(buffer, g_sPreviousMapColor);
    return Plugin_Continue;
}

public Action OnClientSayCommand(int client, const char[] command, const char[] sArgs)
{
    if (WordFilter_ShouldBlock(sArgs))
    {
        if (g_sAdminWebhook[0])
        {
            char name[MAX_NAME_LENGTH];
            char steamid[64];
            char steamid2[32];
            if (IsValidClient(client))
            {
                Format(name, sizeof(name), "%N", client);
                int playerUserID = GetClientUserId(client);
                strcopy(steamid, sizeof(steamid), g_Players[playerUserID].SteamID64);
                strcopy(steamid2, sizeof(steamid2), g_Players[playerUserID].SteamID2);
            }
            else
            {
                strcopy(name, sizeof(name), "CONSOLE");
                steamid[0]  = '\0';
                steamid2[0] = '\0';
            }

            DiscordWebHook hook = new DiscordWebHook(g_sAdminWebhook);
            hook.SetUsername("WordFilter");

            DiscordEmbed embed = new DiscordEmbed();
            embed.SetColor(g_sBlockedMessageColor);

            char title[MAX_BUFFER_LENGTH];
            Format(title, sizeof(title), "%T", "Blocked Message Title", LANG_SERVER, name, steamid);
            embed.AddField(new DiscordEmbedField("", title, false));

            // Message content as the next field
            embed.AddField(new DiscordEmbedField("", sArgs, false));

            if (steamid2[0])
            {
                DiscordEmbedFooter footer = new DiscordEmbedFooter(steamid2);
                embed.WithFooter(footer);
            }

            hook.Embed(embed);
            hook.Send();
            delete hook;
        }
        return Plugin_Handled;
    }

    return Plugin_Continue;
}

public void OnClientSayCommand_Post(int client, const char[] command, const char[] sArgs)
{
    if (WordFilter_ShouldBlock(sArgs)) return;

    char prefixes[10][16];
    int  prefixCount = ExplodeString(g_sHideCommands, ",", prefixes, sizeof(prefixes), sizeof(prefixes[]));

    for (int i = 0; i < prefixCount; i++)
    {
        if (StrContains(sArgs, prefixes[i], false) == 0)
        {
            return;
        }
    }

    // Replace '@' character to prevent players from mentioning in Discord
    char buffer[128];
    strcopy(buffer, sizeof(buffer), sArgs);
    if (StrContains(buffer, "@", false) != -1)
    {
        ReplaceString(buffer, sizeof(buffer), "@", "＠");
    }
    // Prevent '{color}' tags from showing up on Discord
    char colorTag[128];
    int  pos = 0;
    int  len = strlen(buffer);
    for (int i = 0; i < len; i++)
    {
        if (buffer[i] == '{')
        {
            while (i < len && buffer[i] != '}')
            {
                i++;
            }
        }
        else
        {
            colorTag[pos++] = buffer[i];
        }
    }
    colorTag[pos] = '\0';
    strcopy(buffer, sizeof(buffer), colorTag);

    char steamID[128];
    if (StrEqual(g_sShowSteamID, "bottom"))
    {
        Format(steamID, sizeof(steamID), "%s\n-# > [`%s`](<http://www.steamcommunity.com/profiles/%s>)", buffer, g_Players[GetClientUserId(client)].SteamID2, g_Players[GetClientUserId(client)].SteamID64);
        PrintToDiscordSay(client ? GetClientUserId(client) : 0, steamID);
    }
    else if (StrEqual(g_sShowSteamID, "top"))
    {
        Format(steamID, sizeof(steamID), "-# > [`%s`](<http://www.steamcommunity.com/profiles/%s>)\n%s", g_Players[GetClientUserId(client)].SteamID2, g_Players[GetClientUserId(client)].SteamID64, buffer);
        PrintToDiscordSay(client ? GetClientUserId(client) : 0, steamID);
    }
    else if (StrEqual(g_sShowSteamID, "prepend"))
    {
        Format(steamID, sizeof(steamID), "[`%s`](<http://www.steamcommunity.com/profiles/%s>) : %s", g_Players[GetClientUserId(client)].SteamID2, g_Players[GetClientUserId(client)].SteamID64, buffer);
        PrintToDiscordSay(client ? GetClientUserId(client) : 0, steamID);
    }
    else if (StrEqual(g_sShowSteamID, "append"))
    {
        Format(steamID, sizeof(steamID), "%s — [`%s`](<http://www.steamcommunity.com/profiles/%s>)", buffer, g_Players[GetClientUserId(client)].SteamID2, g_Players[GetClientUserId(client)].SteamID64);
        PrintToDiscordSay(client ? GetClientUserId(client) : 0, steamID);
    }
    else if (StrEqual(g_sShowSteamID, "message"))
    {
        Format(steamID, sizeof(steamID), "[%s](<http://www.steamcommunity.com/profiles/%s>)", buffer, g_Players[GetClientUserId(client)].SteamID64);
        PrintToDiscordSay(client ? GetClientUserId(client) : 0, steamID);
    }
    else
    {
        PrintToDiscordSay(client ? GetClientUserId(client) : 0, buffer);
    }
}

public void PrintToDiscord(int userid, const char[] color, const char[] msg, any...)
{
    if (!g_cvServerToDiscord.BoolValue || !g_cvMessage.BoolValue)
    {
        return;
    }

    int client = GetClientOfUserId(userid);
    if (!IsValidClient(client))
    {
        LogError("PrintToDiscord called with invalid client (userid: %d)", userid);
        return;
    }

    // Check if webhook URL is valid
    if (g_sDiscordWebhook[0] == '\0')
    {
        LogError("PrintToDiscord failed: Discord webhook URL is empty");
        return;
    }

    DiscordWebHook hook = new DiscordWebHook(g_sDiscordWebhook);
    if (hook == null)
    {
        LogError("Failed to create Discord webhook");
        return;
    }

    if (g_cvServerToDiscordAvatars.BoolValue)
    {
        hook.SetAvatar(g_Players[userid].AvatarURL);
    }

    char buffer[128];
    Format(buffer, sizeof(buffer), "%N", client);
    hook.SetUsername(buffer);

    DiscordEmbed Embed = new DiscordEmbed();
    if (Embed == null)
    {
        LogError("Failed to create Discord embed");
        delete hook;
        return;
    }

    Embed.SetColor(color);

    char playerName[MAX_NAME_LENGTH];

    if (g_Players[userid].SteamID64[0] != '\0')
    {
        Format(playerName, sizeof(playerName), "[%N](http://www.steamcommunity.com/profiles/%s)", client, g_Players[userid].SteamID64);
        Embed.WithFooter(new DiscordEmbedFooter(g_Players[userid].SteamID2));
    }
    else
    {
        Format(playerName, sizeof(playerName), "%N", client);
    }

    // Embed.AddField(new DiscordEmbedField("", playerName, true));
    Embed.AddField(new DiscordEmbedField("", msg, true));

    hook.Embed(Embed);
    hook.Send();
    delete hook;
}

public void PrintToDiscordSay(int userid, const char[] msg, any...)
{
    if (!g_cvServerToDiscord.BoolValue) return;

    int            client = userid ? GetClientOfUserId(userid) : 0;
    char           formattedMessage[256];
    DiscordWebHook hook = new DiscordWebHook(g_sDiscordWebhook);

    if (!IsValidClient(client))
    {
        if (!g_cvServerMessage.BoolValue)
        {
            delete hook;
            return;
        }

        hook.SetUsername("CONSOLE");
        Format(formattedMessage, sizeof(formattedMessage), "```%s```", msg);

        DiscordEmbed Embed = new DiscordEmbed();
        Embed.SetColor(g_sServerMessageColor);
        Embed.AddField(new DiscordEmbedField("", formattedMessage, false));
        hook.Embed(Embed);
    }
    else
    {
        if (g_cvServerToDiscordAvatars.BoolValue)
        {
            hook.SetAvatar(g_Players[userid].AvatarURL);
        }

        char buffer[128];
        if (StrEqual(g_sShowSteamID, "name"))
        {
            Format(buffer, sizeof(buffer), "%N [%s]", client, g_Players[GetClientUserId(client)].SteamID2);
        }
        else
        {
            Format(buffer, sizeof(buffer), "%N", client);
        }
        hook.SetUsername(buffer);
        Format(formattedMessage, sizeof(formattedMessage), "%s", msg);
        hook.SetContent(formattedMessage);
    }

    hook.Send();
    delete hook;
}

public void PrintToDiscordMapChange(const char[] map, const char[] color)
{
    if (!g_cvServerToDiscord.BoolValue || !g_cvMapChangeMessage.BoolValue)
    {
        return;
    }

    // Use specific webhook if set, otherwise fallback to main webhook
    char webhook[256];
    if (g_sMapStatusWebhook[0])
        strcopy(webhook, sizeof(webhook), g_sMapStatusWebhook);
    else
        strcopy(webhook, sizeof(webhook), g_sDiscordWebhook);
    DiscordWebHook hook = new DiscordWebHook(webhook);
    hook.SetUsername("Server Status");

    DiscordEmbed Embed = new DiscordEmbed();
    Embed.SetColor(color);

    if (g_cvShowServerName.BoolValue)
    {
        char hostname[512];
        FindConVar("hostname").GetString(hostname, sizeof(hostname));
        char phrase[64];
        Format(phrase, sizeof(phrase), "%T", "Server Name", LANG_SERVER);
        Embed.AddField(new DiscordEmbedField(phrase, hostname, false));
    }

    if (g_cvShowServerTags.BoolValue)
    {
        char sv_tags[128];
        FindConVar("sv_tags").GetString(sv_tags, sizeof(sv_tags));
        char phrase[64];
        Format(phrase, sizeof(phrase), "%T", "Server Tags", LANG_SERVER);
        Format(sv_tags, sizeof(sv_tags), "-# `%s`", sv_tags);
        Embed.AddField(new DiscordEmbedField(phrase, sv_tags, false));
    }

    char formattedMapString[MAX_BUFFER_LENGTH];
    FormatMapStringForDiscord(map, formattedMapString, sizeof(formattedMapString));

    char phrase[64];
    Format(phrase, sizeof(phrase), "%T", "Current Map", LANG_SERVER);
    Embed.AddField(new DiscordEmbedField(phrase, formattedMapString, true));

    char buffer[512];
    Format(buffer, sizeof(buffer), "%d/%d", GetOnlinePlayers(), GetMaxHumanPlayers());
    Format(phrase, sizeof(phrase), "%T", "Player Count", LANG_SERVER);
    Embed.AddField(new DiscordEmbedField(phrase, buffer, true));

    int  ip[4];
    char ipStr[MAX_BUFFER_LENGTH];
    if (SteamWorks_GetPublicIP(ip))
    {
        int hostport = GetConVarInt(FindConVar("hostport"));
        Format(ipStr, sizeof(ipStr), "steam://connect/%d.%d.%d.%d:%i", ip[0], ip[1], ip[2], ip[3], hostport);
        DiscordEmbedFooter footer = new DiscordEmbedFooter(ipStr, "https://imgur.com/lgka1Tp.png");
        Embed.WithFooter(footer);
    }

    hook.Embed(Embed);
    hook.Send();
    delete hook;
}

public void PrintToDiscordPreviousMap(const char[] map, const char[] color)
{
    if (!g_cvServerToDiscord.BoolValue || !g_cvMapChangeMessage.BoolValue)
    {
        return;
    }

    // Use specific webhook if set, otherwise fallback to main webhook
    char webhook[256];
    if (g_sMapStatusWebhook[0])
        strcopy(webhook, sizeof(webhook), g_sMapStatusWebhook);
    else
        strcopy(webhook, sizeof(webhook), g_sDiscordWebhook);
    DiscordWebHook hook = new DiscordWebHook(webhook);
    hook.SetUsername("Server Status");

    DiscordEmbed Embed = new DiscordEmbed();
    Embed.SetColor(color);

    if (g_cvShowServerName.BoolValue)
    {
        char hostname[512];
        FindConVar("hostname").GetString(hostname, sizeof(hostname));
        char phrase[64];
        Format(phrase, sizeof(phrase), "%T", "Server Name", LANG_SERVER);
        Embed.AddField(new DiscordEmbedField(phrase, hostname, false));
    }

    if (g_cvShowServerTags.BoolValue)
    {
        char sv_tags[128];
        FindConVar("sv_tags").GetString(sv_tags, sizeof(sv_tags));
        char phrase[64];
        Format(phrase, sizeof(phrase), "%T", "Server Tags", LANG_SERVER);
        Format(sv_tags, sizeof(sv_tags), "-# `%s`", sv_tags);
        Embed.AddField(new DiscordEmbedField(phrase, sv_tags, false));
    }

    char formattedMapString[MAX_BUFFER_LENGTH];
    FormatMapStringForDiscord(map, formattedMapString, sizeof(formattedMapString));

    char phrase[64];
    Format(phrase, sizeof(phrase), "%T", "Previous Map", LANG_SERVER);
    Embed.AddField(new DiscordEmbedField(phrase, formattedMapString, true));

    int  ip[4];
    char ipStr[MAX_BUFFER_LENGTH];
    if (SteamWorks_GetPublicIP(ip))
    {
        int hostport = GetConVarInt(FindConVar("hostport"));
        Format(ipStr, sizeof(ipStr), "steam://connect/%d.%d.%d.%d:%i", ip[0], ip[1], ip[2], ip[3], hostport);
        DiscordEmbedFooter footer = new DiscordEmbedFooter(ipStr, "https://imgur.com/lgka1Tp.png");
        Embed.WithFooter(footer);
    }

    hook.Embed(Embed);
    hook.Send();
    delete hook;
}

public void PrintToChannel(char[] webhook, const char[] msg, const char[] color)
{
    DiscordWebHook hook = new DiscordWebHook(webhook);
    hook.SetUsername("Server Status");

    DiscordEmbed Embed = new DiscordEmbed();
    Embed.SetColor(color);

    Embed.AddField(new DiscordEmbedField("", msg, false));

    hook.Embed(Embed);
    hook.Send();
    delete hook;
}

public void AnnounceToChannel(char[] webhook, const char[] msg, const char[] color)
{
    DiscordWebHook hook = new DiscordWebHook(webhook);
    hook.SetUsername("Server Status");

    DiscordEmbed Embed = new DiscordEmbed();
    Embed.SetColor(color);

    Embed.AddField(new DiscordEmbedField(msg, "", false));

    hook.Embed(Embed);
    hook.Send();
    delete hook;
}

public Action Timer_GetGuildList(Handle timer)
{
    g_Bot.GetGuild(g_sDiscordServerId, false, OnGuildsReceived);
    return Plugin_Continue;
}

public void OnGuildsReceived(DiscordBot bot, DiscordGuild guild)
{
    g_Bot.GetChannel(g_sChannelId, OnRelayChannelReceived);
    g_Bot.GetChannel(g_sRCONChannelId, OnRCONChannelReceived);
}

public void OnRelayChannelReceived(DiscordBot bot, DiscordChannel channel)
{
    if (g_Bot == null || channel == null)
    {
        LogError("Invalid Bot or Channel!");
        return;
    }

    if (g_Bot.IsListeningToChannel(channel)) return;

    if (g_cvDiscordToServer.BoolValue)
    {
        char channelName[64];
        channel.GetName(channelName, sizeof(channelName));

        g_Bot.StartListeningToChannel(channel, OnDiscordMessageSent);
        PrintToServer("Listening to #%s for messages...", channelName);
        if (!g_ChatAnnounced && g_cvListenAnnounce.BoolValue)
        {
            char phrase[64];
            Format(phrase, sizeof(phrase), "%T", "Listening to Chat", LANG_SERVER);
            // Use specific webhook if set, otherwise fallback to main webhook
            char webhook[256];
            if (g_sListenChatWebhook[0])
                strcopy(webhook, sizeof(webhook), g_sListenChatWebhook);
            else
                strcopy(webhook, sizeof(webhook), g_sDiscordWebhook);
            PrintToChannel(webhook, phrase, g_sListenAnnounceColor);
            g_ChatAnnounced = true;
        }
    }
}

public void OnRCONChannelReceived(DiscordBot bot, DiscordChannel channel)
{
    if (g_Bot == null || channel == null)
    {
        LogError("Invalid Bot or Channel!");
        return;
    }

    if (g_Bot.IsListeningToChannel(channel))
    {
        return;
    }

    if (g_cvRCONDiscordToServer.BoolValue)
    {
        char channelName[64];
        channel.GetName(channelName, sizeof(channelName));

        g_Bot.StartListeningToChannel(channel, OnDiscordMessageSent);
        PrintToServer("Listening to #%s for RCON commands...", channelName);
        if (!g_RCONAnnounced && g_cvListenAnnounce.BoolValue)
        {
            char phrase[64];
            Format(phrase, sizeof(phrase), "%T", "Listening to RCON", LANG_SERVER);
            // Use specific webhook if set, otherwise fallback to main webhook
            char webhook[256];
            if (g_sListenRCONWebhook[0])
                strcopy(webhook, sizeof(webhook), g_sListenRCONWebhook);
            else
                strcopy(webhook, sizeof(webhook), g_sRCONWebhook);
            PrintToChannel(webhook, phrase, g_sListenAnnounceColor);
            g_RCONAnnounced = true;
        }
    }
}

public void OnDiscordMessageSent(DiscordBot bot, DiscordChannel chl, DiscordMessage discordmessage)
{
    /*
     * CHAT
     */
    DiscordUser author = discordmessage.GetAuthor();
    if (author.IsBot)
    {
        delete author;
        return;
    }

    char id[20];
    chl.GetID(id, sizeof(id));

    char message[512];
    discordmessage.GetContent(message, sizeof(message));

    if (StrEqual(id, g_sChannelId))
    {
        char discorduser[32];
        author.GetUsername(discorduser, sizeof(discorduser));

        char chatMessage[256];
        if (discordmessage.Type == REPLY)
        {
            Format(
                chatMessage, sizeof(chatMessage),
                "%T", "Discord To Server Reply", LANG_SERVER,
                discorduser, message);
        }
        else
        {
            Format(
                chatMessage, sizeof(chatMessage),
                "%T", "Discord To Server", LANG_SERVER,
                discorduser, message);
        }

        char consoleMessage[256];
        Format(consoleMessage, sizeof(consoleMessage), "%T", "Discord To Server", LANG_SERVER, discorduser, message);

        CPrintToChatAll(chatMessage);
        PrintToServer(consoleMessage);
        delete author;
    }
    /*
     * RCON
     */
    if (StrEqual(id, g_sRCONChannelId))
    {
        if (g_cvPrintRCONResponse.BoolValue)
        {
            char response[2048];
            ServerCommandEx(response, sizeof(response), "%s", message);

            if (response[0] == '\0')
            {
                Format(response, sizeof(response), "%T", "RCON Print Error", LANG_SERVER);
            }
            else if (StrContains(response, "Unknown Command", false) == 0)
            {
                DiscordEmoji emoji = DiscordEmoji.FromName("🚫");
                g_Bot.CreateReaction(chl, discordmessage, emoji);
                return;
            }
            else
            {
                DiscordEmoji emoji = DiscordEmoji.FromName("✅");
                g_Bot.CreateReaction(chl, discordmessage, emoji);
                Format(response, sizeof(response), "%T", "RCON Output", LANG_SERVER, response);
            }

            DiscordWebHook hook = new DiscordWebHook(g_sRCONWebhook);
            hook.SetUsername("RCON");

            DiscordEmbed Embed = new DiscordEmbed();

            Embed.SetColor(g_sPrintRCONResponseColor);
            Embed.AddField(new DiscordEmbedField("", response, false));

            Format(message, sizeof(message), "%T", "RCON Input", LANG_SERVER, message);
            Embed.AddField(new DiscordEmbedField("", message, false));
            // Embed.WithFooter(new DiscordEmbedFooter(message));

            hook.Embed(Embed);
            hook.Send();
            delete hook;
        }
        else
        {
            ServerCommand(message);
        }
    }
}

stock bool IsValidClient(int client)
{
    return client > 0 && client <= MaxClients && IsClientConnected(client) && !IsFakeClient(client) && !IsClientSourceTV(client) && IsClientInGame(client);
}

stock int GetOnlinePlayers()
{
    int count;
    for (int i = 1; i <= MaxClients; i++)
    {
        if (IsClientConnected(i) && !IsFakeClient(i) && !IsClientSourceTV(i))
        {
            count++;
        }
    }
    return count;
}

bool VerifyCriticalConVars()
{
    // Check critical ConVars
    bool bValid = true;

    if (!g_sDiscordWebhook[0])
    {
        LogError("Critical ConVar missing: discrelay_discordwebhook is empty");
        bValid = false;
    }

    if (g_cvDiscordToServer.BoolValue && !g_sDiscordBotToken[0])
    {
        LogError("Critical ConVar missing: discrelay_discordbottoken is empty but discrelay_discordtoserver is enabled");
        bValid = false;
    }

    if (g_cvDiscordToServer.BoolValue && !g_sChannelId[0])
    {
        LogError("Critical ConVar missing: discrelay_channelid is empty but discrelay_discordtoserver is enabled");
        bValid = false;
    }

    if (g_cvRCONDiscordToServer.BoolValue && !g_sRCONChannelId[0])
    {
        LogError("Critical ConVar missing: discrelay_rcon_channelid is empty but discrelay_rcon_enabled is enabled");
        bValid = false;
    }

    return bValid;
}

void LoadAllConVars(bool bSilent = false)
{
    // Load all ConVars from convars.sp
    g_cvSteamApiKey.GetString(g_sSteamApiKey, sizeof(g_sSteamApiKey));
    g_cvDiscordBotToken.GetString(g_sDiscordBotToken, sizeof(g_sDiscordBotToken));
    g_cvDiscordWebhook.GetString(g_sDiscordWebhook, sizeof(g_sDiscordWebhook));
    g_cvRCONWebhook.GetString(g_sRCONWebhook, sizeof(g_sRCONWebhook));
    g_cvDiscordServerId.GetString(g_sDiscordServerId, sizeof(g_sDiscordServerId));
    g_cvChannelId.GetString(g_sChannelId, sizeof(g_sChannelId));
    g_cvRCONChannelId.GetString(g_sRCONChannelId, sizeof(g_sRCONChannelId));
    g_cvAdminWebhook.GetString(g_sAdminWebhook, sizeof(g_sAdminWebhook));
    g_cvShowSteamID.GetString(g_sShowSteamID, sizeof(g_sShowSteamID));
    g_cvHideCommands.GetString(g_sHideCommands, sizeof(g_sHideCommands));
    g_cvAdminRole.GetString(g_sAdminRole, sizeof(g_sAdminRole));

    // Load all color ConVars
    g_cvServerMessageColor.GetString(g_sServerMessageColor, sizeof(g_sServerMessageColor));
    g_cvListenAnnounceColor.GetString(g_sListenAnnounceColor, sizeof(g_sListenAnnounceColor));
    g_cvServerHibernationEnterColor.GetString(g_sServerHibernationEnterColor, sizeof(g_sServerHibernationEnterColor));
    g_cvServerHibernationExitColor.GetString(g_sServerHibernationExitColor, sizeof(g_sServerHibernationExitColor));
    g_cvConsoleMessageColor.GetString(g_sConsoleMessageColor, sizeof(g_sConsoleMessageColor));
    g_cvConnectMessageColor.GetString(g_sConnectMessageColor, sizeof(g_sConnectMessageColor));
    g_cvDisconnectMessageColor.GetString(g_sDisconnectMessageColor, sizeof(g_sDisconnectMessageColor));
    g_cvBanMessageColor.GetString(g_sBanMessageColor, sizeof(g_sBanMessageColor));
    g_cvCurrentMapColor.GetString(g_sCurrentMapColor, sizeof(g_sCurrentMapColor));
    g_cvPreviousMapColor.GetString(g_sPreviousMapColor, sizeof(g_sPreviousMapColor));
    g_cvPrintRCONResponseColor.GetString(g_sPrintRCONResponseColor, sizeof(g_sPrintRCONResponseColor));
    g_cvServerStartColor.GetString(g_sServerStartColor, sizeof(g_sServerStartColor));
    g_cvBlockedMessageColor.GetString(g_sBlockedMessageColor, sizeof(g_sBlockedMessageColor));

    // Load additional webhook ConVars
    g_cvServerHibernationWebhook.GetString(g_sServerHibernationWebhook, sizeof(g_sServerHibernationWebhook));
    g_cvMapStatusWebhook.GetString(g_sMapStatusWebhook, sizeof(g_sMapStatusWebhook));
    g_cvListenChatWebhook.GetString(g_sListenChatWebhook, sizeof(g_sListenChatWebhook));
    g_cvListenRCONWebhook.GetString(g_sListenRCONWebhook, sizeof(g_sListenRCONWebhook));

    // Load filter words
    g_cvFilterWords.GetString(g_sFilterWords, sizeof(g_sFilterWords));

    // Update cooldown value
    g_fCallAdminCooldown = g_cvCallAdminCooldown.FloatValue;

    // Reload word filter
    LoadWordFilter();

    if (!bSilent)
    {
        PrintToServer("[Discord Relay] All ConVars loaded");
    }
}

public Action Timer_RetryConVarLoading(Handle timer)
{
    // Check if this is the active timer
    if (timer != g_hConVarRetryTimer)
        return Plugin_Stop;

    g_hConVarRetryTimer = null;
    g_iConVarRetryCount++;

    PrintToServer("[Discord Relay] Retry attempt %d/%d for ConVar loading", g_iConVarRetryCount, MAX_RETRY_ATTEMPTS);

    // Load all ConVars silently during retries
    LoadAllConVars(true);

    // Verify critical ConVars
    if (VerifyCriticalConVars())
    {
        PrintToServer("[Discord Relay] ConVars successfully loaded on retry attempt %d", g_iConVarRetryCount);
        g_bConVarsLoaded = true;

        // Initialize Discord functionality
        InitializeDiscordFunctionality();
        return Plugin_Stop;
    }

    // Check if we've reached max retries
    if (g_iConVarRetryCount >= MAX_RETRY_ATTEMPTS)
    {
        LogError("[Discord Relay] Failed to load critical ConVars after %d attempts", MAX_RETRY_ATTEMPTS);
        return Plugin_Stop;
    }

    // Schedule another retry
    g_hConVarRetryTimer = CreateTimer(RETRY_DELAY, Timer_RetryConVarLoading, _, TIMER_FLAG_NO_MAPCHANGE);
    return Plugin_Stop;
}

void InitializeDiscordFunctionality()
{
    // Only proceed if ConVars are loaded
    if (!g_bConVarsLoaded)
    {
        LogError("[Discord Relay] Attempted to initialize Discord functionality before ConVars were loaded");
        return;
    }

    // Create bot if needed
    if ((g_cvDiscordToServer.BoolValue || g_cvRCONDiscordToServer.BoolValue) && !g_Bot)
    {
        PrintToServer("[Discord Relay] Creating Discord bot...");
        g_Bot = new DiscordBot(g_sDiscordBotToken);
        CreateTimer(1.0, Timer_GetGuildList, _, TIMER_FLAG_NO_MAPCHANGE);
    }

    // Send map change message if enabled
    if (g_cvMapChangeMessage.BoolValue)
    {
        char buffer[64];
        GetCurrentMap(buffer, sizeof(buffer));
        PrintToDiscordMapChange(buffer, g_sCurrentMapColor);
    }
}

public Action Command_DiscordRelayStatus(int client, int args)
{
    PrintConVarStatus();

    if (client > 0)
    {
        ReplyToCommand(client, "[DiscordRelay] ConVar status printed to server console.");
        ReplyToCommand(client, "[DiscordRelay] Bot initialized: %s", g_Bot != null ? "Yes" : "No");
    }

    return Plugin_Handled;
}

void PrintConVarStatus()
{
    PrintToServer("===== DiscordRelay ConVar Status =====");

    // RCON settings
    PrintToServer("RCON Enabled: %s", g_cvRCONDiscordToServer.BoolValue ? "Yes" : "No");
    PrintToServer("RCON Channel ID: %s", g_sRCONChannelId[0] ? g_sRCONChannelId : "Not Set");

    // Print webhooks with only last 16 chars visible for security
    char webhookDisplay[64];

    // RCON Webhook
    if (g_sRCONWebhook[0])
    {
        int len = strlen(g_sRCONWebhook);
        if (len > 16)
            Format(webhookDisplay, sizeof(webhookDisplay), "...%s", g_sRCONWebhook[len - 16]);
        else
            Format(webhookDisplay, sizeof(webhookDisplay), "...%s", g_sRCONWebhook);
    }
    else
    {
        strcopy(webhookDisplay, sizeof(webhookDisplay), "Not Set");
    }
    PrintToServer("RCON Webhook: %s", webhookDisplay);

    // Discord Channel ID
    PrintToServer("Discord Channel ID: %s", g_sChannelId[0] ? g_sChannelId : "Not Set");

    // Discord Webhook
    if (g_sDiscordWebhook[0])
    {
        int len = strlen(g_sDiscordWebhook);
        if (len > 16)
            Format(webhookDisplay, sizeof(webhookDisplay), "...%s", g_sDiscordWebhook[len - 16]);
        else
            Format(webhookDisplay, sizeof(webhookDisplay), "...%s", g_sDiscordWebhook);
    }
    else
    {
        strcopy(webhookDisplay, sizeof(webhookDisplay), "Not Set");
    }
    PrintToServer("Discord Webhook: %s", webhookDisplay);

    // Listen Chat Webhook
    if (g_sListenChatWebhook[0])
    {
        int len = strlen(g_sListenChatWebhook);
        if (len > 16)
            Format(webhookDisplay, sizeof(webhookDisplay), "...%s", g_sListenChatWebhook[len - 16]);
        else
            Format(webhookDisplay, sizeof(webhookDisplay), "...%s", g_sListenChatWebhook);
    }
    else
    {
        strcopy(webhookDisplay, sizeof(webhookDisplay), "Not Set");
    }
    PrintToServer("Listen Chat Webhook: %s", webhookDisplay);

    // Map Status Webhook
    if (g_sMapStatusWebhook[0])
    {
        int len = strlen(g_sMapStatusWebhook);
        if (len > 16)
            Format(webhookDisplay, sizeof(webhookDisplay), "...%s", g_sMapStatusWebhook[len - 16]);
        else
            Format(webhookDisplay, sizeof(webhookDisplay), "...%s", g_sMapStatusWebhook);
    }
    else
    {
        strcopy(webhookDisplay, sizeof(webhookDisplay), "Not Set");
    }
    PrintToServer("Map Status Webhook: %s", webhookDisplay);

    // Server Hibernation Webhook
    if (g_sServerHibernationWebhook[0])
    {
        int len = strlen(g_sServerHibernationWebhook);
        if (len > 16)
            Format(webhookDisplay, sizeof(webhookDisplay), "...%s", g_sServerHibernationWebhook[len - 16]);
        else
            Format(webhookDisplay, sizeof(webhookDisplay), "...%s", g_sServerHibernationWebhook);
    }
    else
    {
        strcopy(webhookDisplay, sizeof(webhookDisplay), "Not Set");
    }
    PrintToServer("Server Hibernation Webhook: %s", webhookDisplay);

    // Admin Webhook
    if (g_sAdminWebhook[0])
    {
        int len = strlen(g_sAdminWebhook);
        if (len > 16)
            Format(webhookDisplay, sizeof(webhookDisplay), "...%s", g_sAdminWebhook[len - 16]);
        else
            Format(webhookDisplay, sizeof(webhookDisplay), "...%s", g_sAdminWebhook);
    }
    else
    {
        strcopy(webhookDisplay, sizeof(webhookDisplay), "Not Set");
    }
    PrintToServer("Admin Webhook: %s", webhookDisplay);

    PrintToServer("======= End of ConVar Status =======");
}

/**
 * Formats a map string for Discord embedding with download and website links
 *
 * @param map               Map name to format
 * @param formattedString   Buffer to store the formatted string
 * @param maxLength         Maximum length of the buffer
 */
void FormatMapStringForDiscord(const char[] map, char[] formattedString, int maxLength)
{
    if (StrContains(map, "workshop/", false) != -1)
    {
        char workshopId[2][64];
        ExplodeString(map, "/", workshopId, sizeof(workshopId), sizeof(workshopId[]));

        char mapName[2][64];
        ExplodeString(workshopId[1], ".ugc", mapName, sizeof(mapName), sizeof(mapName[]));

        char workshopUrl[256];
        Format(workshopUrl, sizeof(workshopUrl), "https://steamcommunity.com/sharedfiles/filedetails/?id=%s", mapName[1]);

        char websiteMapPageUrl[256];
        Format(websiteMapPageUrl, sizeof(websiteMapPageUrl), "https://surfing4.fun/?sv=surf&m=%s", mapName[0]);

        Format(formattedString, maxLength, "[⬇️](%s) [%s](%s)", workshopUrl, mapName[0], websiteMapPageUrl);
    }
    else
    {
        char sv_downloadurl[512];
        FindConVar("sv_downloadurl").GetString(sv_downloadurl, sizeof(sv_downloadurl));

        char fastdlUrl[512];
        Format(fastdlUrl, sizeof(fastdlUrl), "%s/maps/%s.bsp.bz2", sv_downloadurl, map);

        char websiteMapPageUrl[256];
        Format(websiteMapPageUrl, sizeof(websiteMapPageUrl), "https://surfing4.fun/?sv=surf&m=%s", map);

        Format(formattedString, maxLength, "[⬇️](%s) [%s](%s)", fastdlUrl, map, websiteMapPageUrl);
    }
}