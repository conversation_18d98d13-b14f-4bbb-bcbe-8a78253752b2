#pragma newdecls required
#pragma semicolon 1

#include <sourcemod>
#include <shavit>

// Include sh-servers as optional. The plugin will check for its availability.
#undef REQUIRE_PLUGIN
#include <sh-servers>

#define TIMER_INTERVAL 10.0

Database      gH_SQL                 = null;
int           gI_LastRecordTimestamp = 0;
int           gI_Driver              = Driver_unknown;

chatstrings_t gS_ChatStrings;

enum struct LocalWRInfo
{                                    // enum struct is used for C-style data structures
    int   auth;                      // Player's AccountID (SteamID3)
    char  map[PLATFORM_MAX_PATH];    // Map name
    int   track;                     // Track ID (0 for main, >0 for bonus)
    int   style;                     // Style ID
    float time;                      // Record time
    float timestamp;                 // Game time when this local WR was recorded by the plugin
}

ArrayList g_alLocalWRs;    // List to hold LocalWRInfo
public Plugin myinfo =
{
    name        = "S4F - Cross server record relayer",
    author      = "Proxychains",
    description = "[shavit] Relay record messages across servers connected to the same database",
    version     = "1.3",
    url         = "https://github.com/surfing4fun"
};

// Add global variables to store server info
char g_sMatchingServerName[64];
char g_sMatchingServerAddress[32];
bool g_bMatchingServerFound = false;
bool g_bServerInfoCached    = false;

public void OnPluginStart()
{
    // Initialize the ArrayList for local WRs
    g_alLocalWRs = new ArrayList(sizeof(LocalWRInfo));

    gH_SQL       = Shavit_GetDatabase(gI_Driver);
    if (gH_SQL == null)
    {
        LogError("[RecordRelay] Failed to get database handle from Shavit core. Plugin will not function.");
        SetFailState("Failed to get database handle from Shavit core. Ensure Shavit's core plugin is loaded and configured.");
        return;
    }

    gH_SQL.Query(Query_Callback_GetLatestRecordTimestamp, "SELECT MAX(date) FROM wrs");

    RegAdminCmd("sm_testrecordrelay", Command_TestRecordRelay, ADMFLAG_ROOT, "Tests the cross-server record relay with dummy data.");
}

public void Shavit_OnChatConfigLoaded()
{
    Shavit_GetChatStrings(sMessageText, gS_ChatStrings.sText, sizeof(chatstrings_t::sText));
    Shavit_GetChatStrings(sMessageStyle, gS_ChatStrings.sStyle, sizeof(chatstrings_t::sStyle));
    Shavit_GetChatStrings(sMessageWarning, gS_ChatStrings.sWarning, sizeof(chatstrings_t::sWarning));
    Shavit_GetChatStrings(sMessageVariable, gS_ChatStrings.sVariable, sizeof(chatstrings_t::sVariable));
    Shavit_GetChatStrings(sMessageVariable2, gS_ChatStrings.sVariable2, sizeof(chatstrings_t::sVariable2));
}

public void OnPluginEnd()
{
    delete g_alLocalWRs;
}

void Query_Callback_GetLatestRecordTimestamp(Database db, DBResultSet results, const char[] error, any data)
{
    if (results == null)
    {
        LogError("[RecordRelay] Query_Callback_GetLatestRecordTimestamp: Failed to get latest record timestamp from `wrs`. Error = '%s'", error);
        SetFailState("Failed to get latest record timestamp from `wrs`. Error = '%s'", error);
        return;
    }
    if (results.FetchRow())
    {
        if (results.IsFieldNull(0))
        {
            gI_LastRecordTimestamp = 0;
        }
        else
        {
            gI_LastRecordTimestamp = results.FetchInt(0);
        }
    }
    else
    {
        // This case should not be reached if MAX(date) query was successful and results != null
        gI_LastRecordTimestamp = 0;
    }
    // Start the timer regardless of whether timestamp was successfully fetched, it will poll with 0 if needed.
    CreateTimer(TIMER_INTERVAL, Timer_FetchNewRecords);
}

void Query_ProcessNewRecords(Database db, DBResultSet results, const char[] error, any data)
{
    // Re-schedule the timer first, so it always continues
    CreateTimer(TIMER_INTERVAL, Timer_FetchNewRecords);

    if (results == null)
    {
        LogError("[RecordRelay] Query_ProcessNewRecords: Failed to get latest records. Error = '%s'", error);
        return;
    }

    if (!results.FetchRow())
    {
        // LogMessage("[RecordRelay] Query_ProcessNewRecords: No new records found this cycle."); // Can be spammy
        return;
    }

    // LogMessage("[RecordRelay] Query_ProcessNewRecords: Processing new records...");
    do
    {
        int  db_id = results.FetchInt(0);
        char db_playername[65];
        results.FetchString(1, db_playername, sizeof(db_playername));
        char db_mapname[256];
        results.FetchString(2, db_mapname, sizeof(db_mapname));
        int   db_style             = results.FetchInt(3);
        int   db_track             = results.FetchInt(4);
        float db_rectime           = results.FetchFloat(5);
        int   db_auth              = results.FetchInt(6);
        int   db_date              = results.FetchInt(7);
        float db_second_place_time = -1.0;
        if (!results.IsFieldNull(8))
        {
            db_second_place_time = results.FetchFloat(8);
        }

        // Add debug logging for previous_time
        LogMessage("[RecordRelay] Record data - Previous time: %.3f (IsNull: %d)",
                   db_second_place_time, results.IsFieldNull(8));

        // Update gI_LastRecordTimestamp to the date of the current record.
        // Since records are ordered by date ASC, the last assignment in this loop will be the highest.
        gI_LastRecordTimestamp = db_date;

        // LogMessage("[RecordRelay] Processing DB Record - ID: %d, Date: %d. Current gI_LastRecordTimestamp for next poll: %d.", db_id, db_date, gI_LastRecordTimestamp);
        LogMessage("[RecordRelay] Fetched Record - ID: %d, Date: %d, Player: %s, Map: %s, Style: %d, Track: %d, Time: %.3f, Auth: %d, Second Place Time: %.3f",
                   db_id, db_date, db_playername, db_mapname, db_style, db_track, db_rectime, db_auth, db_second_place_time);

        // Check if this record was recently set locally
        bool isLocalRecord = false;
        char currentMap[PLATFORM_MAX_PATH];
        GetCurrentMap(currentMap, sizeof(currentMap));

        // Skip announcing if this record was set on the current server
        for (int i = 0; i < g_alLocalWRs.Length; i++)
        {
            LocalWRInfo localWR;
            g_alLocalWRs.GetArray(i, localWR);

            if (localWR.auth == db_auth && StrEqual(localWR.map, db_mapname, false) && localWR.style == db_style && localWR.track == db_track && FloatAbs(localWR.time - db_rectime) < 0.001)
            {
                // LogMessage("[RecordRelay] Record ID %d (Player: %s, Map: %s) matches a local WR. Marking as local.", db_id, db_playername, db_mapname);
                isLocalRecord = true;
                g_alLocalWRs.Erase(i);
                break;
            }
        }

        bool isDifferentMap = !StrEqual(currentMap, db_mapname, false);
        // LogMessage("[RecordRelay] Record ID %d - isLocalRecord: %s, currentMap: %s, db_mapname: %s, isDifferentMap: %s", db_id, isLocalRecord ? "true" : "false", currentMap, db_mapname, isDifferentMap ? "true" : "false");

        // Only announce if it's not a local record and not on the current map
        if (!isLocalRecord && isDifferentMap)
        {
            ArrayList passData = new ArrayList(6);
            passData.PushString(db_playername);
            passData.PushString(db_mapname);
            passData.Push(db_style);
            passData.Push(db_track);
            passData.Push(view_as<any>(db_rectime));
            passData.Push(view_as<any>(db_second_place_time));

            // Try to find a server running this map before querying the tier
            TryGetServerRunningMap(db_mapname);

            char escapedMapName[PLATFORM_MAX_PATH * 2 + 1];
            gH_SQL.Escape(db_mapname, escapedMapName, sizeof(escapedMapName));

            char tierQuery[300];
            FormatEx(tierQuery, sizeof(tierQuery), "SELECT tier FROM maptiers WHERE map = '%s' LIMIT 1;", escapedMapName);

            LogMessage("[RecordRelay] Record for %s on %s needs announcement.", db_playername, db_mapname);
            gH_SQL.Query(Query_Callback_FetchTierAndAnnounce, tierQuery, passData);
        }
    }
    while (results.FetchRow());
}

void Query_Callback_FetchTierAndAnnounce(Database db, DBResultSet results, const char[] error, ArrayList data)
{
    // Retrieve data from ArrayList
    char playername[65];
    data.GetString(0, playername, sizeof(playername));
    char mapname[PLATFORM_MAX_PATH];
    data.GetString(1, mapname, sizeof(mapname));
    int   style             = data.Get(2);
    int   track             = data.Get(3);
    float rectime           = view_as<float>(data.Get(4));
    float second_place_time = view_as<float>(data.Get(5));

    int   tier              = 0;

    if (results == null)
    {
        // LogError("[RecordRelay] Query_Callback_FetchTierAndAnnounce: Failed to get tier for map '%s'. Error = '%s'. Using default tier 0.", mapname, error);
    }
    else if (results.FetchRow())
    {    // successfully fetched tier
        tier = results.FetchInt(0);
    }
    else
    {
        // LogMessage("[RecordRelay] Query_Callback_FetchTierAndAnnounce: No tier found for map '%s'. Using default tier 0.", mapname);
    }

    AnnounceWR(playername, mapname, style, track, rectime, tier, second_place_time);
}

void AnnounceWR(const char[] playername, const char[] mapname, int style, int track, float rectime, int tier, float second_place_time)
{
    char styleString[32];
    switch (style)
    {
        case 0:
            strcopy(styleString, sizeof(styleString), "Normal");
        case 1:
            strcopy(styleString, sizeof(styleString), "Sideways");
        case 2:
            strcopy(styleString, sizeof(styleString), "W-Only");
        case 3:
            strcopy(styleString, sizeof(styleString), "Half-Sideways");
        case 4:
            strcopy(styleString, sizeof(styleString), "A/D-Only");
        default:
            FormatEx(styleString, sizeof(styleString), "Style %d", style);
    }

    // Format the time to include minutes if necessary
    char sFormattedTime[32];
    if (rectime >= 60.0)
    {
        int   minutes = RoundToFloor(rectime / 60.0);
        float seconds = rectime - float(minutes * 60);
        if (seconds < 10.0)    // Zero-pad seconds if < 10
        {
            FormatEx(sFormattedTime, sizeof(sFormattedTime), "%d:0%.3f", minutes, seconds);
        }
        else
        {
            FormatEx(sFormattedTime, sizeof(sFormattedTime), "%d:%.3f", minutes, seconds);
        }
    }
    else
    {
        FormatEx(sFormattedTime, sizeof(sFormattedTime), "%.3fs", rectime);
    }

    char timeDifferenceString[64];
    if (second_place_time <= 0.0)
    {
        strcopy(timeDifferenceString, sizeof(timeDifferenceString), "");    //  (no 2nd place)
    }
    else
    {
        float diff = second_place_time - rectime;
        if (diff > 0.0)
        {
            FormatEx(timeDifferenceString, sizeof(timeDifferenceString), " (-%.3fs)", diff);
        }
        else
        {
            // This shouldn't happen as WR should be the fastest time
            FormatEx(timeDifferenceString, sizeof(timeDifferenceString), " (?)");
        }
    }

    char tierDisplayString[20];
    if (tier > 0)
    {
        FormatEx(tierDisplayString, sizeof(tierDisplayString), "%d", tier);
    }
    else
    {
        strcopy(tierDisplayString, sizeof(tierDisplayString), "?");
    }

    // --- Main Message: Detailed Record Information ---
    char buffer[256];

    // Different format based on track
    if (track == 0)    // Main track
    {
        FormatEx(buffer, sizeof(buffer),
                 "%s(World Record) %s%s %sfinished %s%s %sin %s%s %s%s %s[%sTier %d, %s%s]",
                 gS_ChatStrings.sWarning,      // (World Record)
                 gS_ChatStrings.sVariable2,    // Player name color
                 playername,                   // Player name
                 gS_ChatStrings.sText,         // "finished"
                 gS_ChatStrings.sVariable,     // Map color
                 mapname,                      // Map name
                 gS_ChatStrings.sText,         // "in"
                 gS_ChatStrings.sVariable2,    // Time color
                 sFormattedTime,               // Time
                 gS_ChatStrings.sVariable2,    // Time difference color
                 timeDifferenceString,         // Time difference
                 gS_ChatStrings.sText,         // Style bracket color [
                 gS_ChatStrings.sStyle,        // colors
                 tier,                         // Tier number
                 styleString,                  // Style name
                 gS_ChatStrings.sText);        // Style bracket color ]
    }
    else    // Bonus track
    {
        FormatEx(buffer, sizeof(buffer),
                 "%s(World Record) %s%s %sfinished %sbonus %d %son %s%s %sin %s%s %s%s %s[%sTier %d, %s%s]",
                 gS_ChatStrings.sWarning,      // (World Record)
                 gS_ChatStrings.sVariable2,    // Player name color
                 playername,                   // Player name
                 gS_ChatStrings.sText,         // "finished"
                 gS_ChatStrings.sVariable,     // Bonus color
                 track,                        // Bonus number
                 gS_ChatStrings.sText,         // "on"
                 gS_ChatStrings.sVariable,     // Map color
                 mapname,                      // Map name
                 gS_ChatStrings.sText,         // "in"
                 gS_ChatStrings.sVariable2,    // Time color
                 sFormattedTime,               // Time
                 gS_ChatStrings.sVariable2,    // Time difference color
                 timeDifferenceString,         // Time difference
                 gS_ChatStrings.sText,         // Style bracket color [
                 gS_ChatStrings.sStyle,        // colors
                 tier,                         // Tier number
                 styleString,                  // Style name
                 gS_ChatStrings.sText);        // Style bracket color ]
    }

    LogMessage("[RecordRelay] Announcing WR: %s finished %s on %s in %s%s [Tier %d, %s]",
               playername, track == 0 ? "main track" : "bonus", mapname, sFormattedTime, timeDifferenceString, tier, styleString);

    Shavit_PrintToChatAll(buffer);

    // Try to find a server running this map
    TryGetServerRunningMap(mapname);

    // Add the server info if available
    if (g_bMatchingServerFound)
    {
        char serverBuffer[256];
        FormatEx(serverBuffer, sizeof(serverBuffer),
                 "%s(World Record) %sNew record on server: %s%s%s | (%s%s%s)",
                 gS_ChatStrings.sWarning,     // (World Record)
                 gS_ChatStrings.sText,        // color reset
                 gS_ChatStrings.sVariable,    // Server name color
                 g_sMatchingServerName,       // Server name
                 gS_ChatStrings.sText,        // color reset | (
                 gS_ChatStrings.sStyle,       // Address color
                 g_sMatchingServerAddress,    // Server address
                 gS_ChatStrings.sText);       // )

        Shavit_PrintToChatAll(serverBuffer);
    }
}

Action Timer_FetchNewRecords(Handle timer)
{
    // LogMessage("[RecordRelay] Timer_FetchNewRecords fired. Current gI_LastRecordTimestamp: %d", gI_LastRecordTimestamp); // Can be spammy
    // Cleanup old entries from the local WRs cache
    if (g_alLocalWRs != null)
    {
        for (int i = g_alLocalWRs.Length - 1; i >= 0; i--)
        {
            LocalWRInfo localWR;
            g_alLocalWRs.GetArray(i, localWR);

            // Remove entries older than 20 seconds
            if (GetGameTime() - localWR.timestamp > 20.0)
            {
                // LogMessage("[RecordRelay] Removing old local WR cache entry: Auth %d, Map %s, Time %.3f (Timestamp: %.2f, Now: %.2f)", localWR.auth, localWR.map, localWR.time, localWR.timestamp, GetGameTime());
                g_alLocalWRs.Erase(i);
            }
        }
    }

    // Subquery to find the previous WR (the time that was just beaten)
    // We assume the Shavit player times table is named 'playertimes'
    char sSubQueryPreviousWR[300];
    FormatEx(sSubQueryPreviousWR, sizeof(sSubQueryPreviousWR),
             "(SELECT pt.time FROM playertimes AS pt WHERE pt.map = wrs.map AND pt.style = wrs.style AND pt.track = wrs.track AND pt.time > wrs.time ORDER BY pt.time ASC LIMIT 1)");

    // Fetch new records
    // LogMessage("[RecordRelay] Current local WR cache size: %d", g_alLocalWRs.Length);
    char query[1536];    // Increased buffer size for the potentially longer query
    FormatEx(query, sizeof(query),
             "SELECT wrs.id, u.name, wrs.map, wrs.style, wrs.track, wrs.time, wrs.auth, wrs.date, %s AS second_place_time " ... "FROM wrs JOIN users u ON wrs.auth = u.auth " ... "WHERE wrs.date > %d AND wrs.auth IS NOT NULL AND wrs.auth != 0 AND wrs.style IN (0, 1, 2, 3, 4) " ... "ORDER BY wrs.date ASC, wrs.id ASC",
             sSubQueryPreviousWR,        // For %s (the subquery string)
             gI_LastRecordTimestamp);    // For %d

    gH_SQL.Query(Query_ProcessNewRecords, query);
    return Plugin_Continue;
}

public void Shavit_OnWorldRecord(int client, int style, float time, int jumps, int strafes, float sync, int track, int stage, float oldwr)
{
    // We only care about full runs (not individual stages) for this global announcer
    if (stage == 0 && g_alLocalWRs != null)
    {
        LocalWRInfo localWR;
        localWR.auth = GetSteamAccountID(client);
        GetCurrentMap(localWR.map, sizeof(localWR.map));
        localWR.style     = style;
        localWR.track     = track;
        localWR.time      = time;
        localWR.timestamp = GetGameTime();
        g_alLocalWRs.Push(localWR);

        char sClientName[MAX_NAME_LENGTH];
        GetClientName(client, sClientName, sizeof(sClientName));
        LogMessage("[RecordRelay] Cached local WR: Player %s (Auth: %d), Map: %s, Style: %d, Track: %d, Time: %.3f", sClientName, localWR.auth, localWR.map, style, track, time);
    }
}

public Action Command_TestRecordRelay(int client, int args)
{
    if (args == 0)
    {
        AnnounceWR("Proxychains", "surf_agony", 0, 0, 63.123, 2, 65.456);
        return Plugin_Handled;
    }

    if (args != 7)
    {
        ReplyToCommand(client, "Usage: sm_testrecordrelay <playername> <mapname> <style> <track> <time> <tier> <second_place_time>");
        ReplyToCommand(client, "Example: !testrecordrelay \"Proxychains\" surf_agony 0 0 63.123 2 65.456");
        return Plugin_Handled;
    }

    char sPlayerName[MAX_NAME_LENGTH];
    GetCmdArg(1, sPlayerName, sizeof(sPlayerName));

    char sMapName[PLATFORM_MAX_PATH];
    GetCmdArg(2, sMapName, sizeof(sMapName));

    int   style           = GetCmdArgInt(3);
    int   track           = GetCmdArgInt(4);
    float time            = GetCmdArgFloat(5);
    int   tier            = GetCmdArgInt(6);
    float secondPlaceTime = GetCmdArgFloat(7);

    AnnounceWR(sPlayerName, sMapName, style, track, time, tier, secondPlaceTime);

    return Plugin_Handled;
}

/**
 * Attempts to find a server running the specified map.
 *
 * @param mapName       The map name to search for
 * @param forceUpdate   Whether to force an update even if we already have cached info
 * @return              True if a server running the map was found, false otherwise
 */
bool TryGetServerRunningMap(const char[] mapName, bool forceUpdate = false)
{
    // Skip if we already have the info cached for this map and not forcing update
    static char s_LastMapQueried[PLATFORM_MAX_PATH];
    if (g_bServerInfoCached && !forceUpdate && StrEqual(s_LastMapQueried, mapName))
    {
        return g_bMatchingServerFound;
    }

    strcopy(s_LastMapQueried, sizeof(s_LastMapQueried), mapName);

    g_bMatchingServerFound = false;    // Assume not found until proven otherwise
    g_bServerInfoCached    = true;     // Mark as cached for this attempt

    char tempName[64];
    char tempAddress[32];

    bool bFound = SH_GetServerRunningMap(mapName, tempName, sizeof(tempName), tempAddress, sizeof(tempAddress));

    if (bFound)
    {
        // Update the cached info
        strcopy(g_sMatchingServerName, sizeof(g_sMatchingServerName), tempName);
        strcopy(g_sMatchingServerAddress, sizeof(g_sMatchingServerAddress), tempAddress);
        LogMessage("[RecordRelay] Found server running map %s: %s (%s)",
                   mapName, g_sMatchingServerName, g_sMatchingServerAddress);
        g_bMatchingServerFound = true;
    }
    else
    {
        // Clear the cache for this map if not found
        g_sMatchingServerName[0]    = '\0';
        g_sMatchingServerAddress[0] = '\0';
        // LogMessage("[RecordRelay] No server found running map %s (or sh-servers not available/native failed)", mapName);
    }

    return g_bMatchingServerFound;
}

public void OnAllPluginsLoaded()
{
    CreateTimer(5.0, Timer_DelayedServerInfoFetch);
}

public void OnLibraryAdded(const char[] name)
{
    if (StrEqual(name, "sh-servers"))
    {    // Reset the cache flag when the library is loaded
        g_bServerInfoCached = false;
        CreateTimer(5.0, Timer_DelayedServerInfoFetch);
    }
}

public Action Timer_DelayedServerInfoFetch(Handle timer)
{
    if (LibraryExists("sh-servers"))
    {
        // LogMessage("[RecordRelay] sh-servers library is available");
        // Don't try to find a server yet - we'll do that when we have a specific map
        g_bServerInfoCached = false;    // Reset the cache to ensure we'll look up servers when needed
    }
    else
    {
        LogMessage("[RecordRelay] sh-servers library is not available");
    }
    return Plugin_Stop;
}

public void OnServerListUpdated(int serverCount)
{
    // Reset the cache when the server list is updated
    g_bServerInfoCached = false;
    // LogMessage("[RecordRelay] Server list updated with %d servers, cache reset", serverCount);
}
