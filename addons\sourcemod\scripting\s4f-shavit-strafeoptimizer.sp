/*
 * shavit's Timer - Strafe Optimizer
 * by prxchns - Based on concepts from various sources.
 *
 * This plugin aims to implement a strafe optimizer that adjusts player
 * view angles for optimal speed gain when pressing A/D strafe keys in air.
 *
 */

#pragma newdecls required
#pragma semicolon 1

#include <sourcemod>
#include <sdktools>
#include <sdkhooks>
// #include <cstrike> // If CS:S/CS:GO specific features are needed beyond SDK.

#include <shavit/core>
#include <shavit/tas-xutax>    // For Vec2DToYaw, GetThetaAngleInAir, AngleNormalize

// Global Variables
bool          g_bOptimizerEnabled[MAXPLAYERS + 1];
float         g_fOptimizerStrength[MAXPLAYERS + 1];
float         g_flLastYaw[MAXPLAYERS + 1];
float         g_fPlayerMinMouseDelta[MAXPLAYERS + 1];       // Per-player setting
float         g_fPlayerActivationFactor[MAXPLAYERS + 1];    // Per-player setting
ConVar        g_cvOptimizerDefaultStrength;

ConVar        g_cvAirAccelerate;
// ConVar g_cvSVSMaxSpeed; // Usually overridden by style's runspeed

ConVar        g_cvOptimizerMinMouseDelta;
ConVar        g_cvOptimizerActivationFactor;

float         g_flAirSpeedCap          = 30.0;    // Default for CS:S, CS:GO might be different or from ConVar
int           g_iSurfaceFrictionOffset = -1;
float         g_fMaxMove               = 400.0;    // Default, CS:GO is 450.0

EngineVersion g_eEngineVersion;

public Plugin myinfo =
{
    name        = "S4F - Strafe Optimizer",
    author      = "Proxychains",
    description = "Adjusts view angles for optimal A/D strafing in air.",
    version     = "1.2",
    url         = "https://github.com/surfing4fun/"
};

public void OnPluginStart()
{
    LoadTranslations("shavit-common.phrases");

    g_cvOptimizerDefaultStrength  = CreateConVar("sm_strafeoptimizer_default_strength", "0.6", "Default strength for strafe optimizer (0.1 to 1.0).", FCVAR_NOTIFY, true, 0.1, true, 1.0);
    g_cvOptimizerMinMouseDelta    = CreateConVar("sm_strafeoptimizer_min_mouse_delta", "0.25", "Minimum mouse yaw change per tick for optimizer to activate. 0 to disable this check.", FCVAR_NOTIFY, true, 0.0, true, 5.0);
    g_cvOptimizerActivationFactor = CreateConVar("sm_strafeoptimizer_activation_factor", "0.0", "Player's mouse turn must be > (NeededTurnToOptimal * Factor) for optimizer to activate. 0 to disable this specific check.", FCVAR_NOTIFY, true, 0.0, true, 1.0);

    AutoExecConfig(true, "plugin.shavit-strafeoptimizer");

    g_cvAirAccelerate = FindConVar("sv_airaccelerate");
    // g_cvSVSMaxSpeed = FindConVar("sv_maxspeed");

    // Removed toggle commands
    RegConsoleCmd("sm_optimizestrength", Command_SetOptimizerStrength, "Sets strafe optimizer strength (custom style). Usage: sm_optimizestrength <0.1-1.0>");    // Using sm_optimizestrength
    RegConsoleCmd("sm_optipower", Command_SetOptimizerStrength, "Sets strafe optimizer strength (custom style). Usage: sm_optimizestrength <0.1-1.0>");           // Using sm_optimizestrength
    RegConsoleCmd("sm_optimousedelta", Command_SetOptimizerMouseDelta, "Sets your minimum mouse delta for optimizer. Usage: sm_optimousedelta <0.0-5.0>");
    RegConsoleCmd("sm_optidelta", Command_SetOptimizerMouseDelta, "Sets your minimum mouse delta for optimizer. Usage: sm_optimousedelta <0.0-5.0>");
    RegConsoleCmd("sm_optifactor", Command_SetOptimizerActivationFactor, "Sets your activation factor for optimizer. Usage: sm_optifactor <0.0-1.0>");
    RegConsoleCmd("sm_checkopti", Command_CheckOptiStatus, "Checks optimizer status for a player. Usage: sm_checkopti <#userid|name>");

    g_eEngineVersion = GetEngineVersion();
    if (g_eEngineVersion == Engine_CSGO)
    {
        g_fMaxMove                  = 450.0;
        ConVar sv_air_max_wishspeed = FindConVar("sv_air_max_wishspeed");
        if (sv_air_max_wishspeed != null)
        {
            g_flAirSpeedCap = sv_air_max_wishspeed.FloatValue;
            // Add hook if it can change mid-game, similar to shavit-tas.sp
            // sv_air_max_wishspeed.AddChangeHook(OnWishSpeedChanged);
        }
    }

    GameData gamedata               = new GameData("shavit.games");
    Address  surfaceFrictionAddress = gamedata.GetMemSig("CBasePlayer->m_surfaceFriction");
    if (surfaceFrictionAddress == Address_Null)
    {
        g_iSurfaceFrictionOffset = -1;
        LogError("[StrafeOptimizer] The address of m_surfaceFriction is null, defaulting friction values");
    }
    else
    {
        // The offset is encoded in the upper 16 bits of the integer at the address.
        // This is specific to how shavit.games stores it.
        int instr                = LoadFromAddress(surfaceFrictionAddress, NumberType_Int32);
        g_iSurfaceFrictionOffset = instr >> 16;
    }
    delete gamedata;

    for (int i = 1; i <= MaxClients; i++)
    {
        if (IsClientConnected(i)) OnClientConnected(i);
    }
}

public void OnClientConnected(int client)
{
    g_bOptimizerEnabled[client]       = false;    // Optimizer is off by default
    g_fOptimizerStrength[client]      = g_cvOptimizerDefaultStrength.FloatValue;
    g_flLastYaw[client]               = 0.0;
    g_fPlayerMinMouseDelta[client]    = -1.0;    // -1.0 indicates to use CVar default
    g_fPlayerActivationFactor[client] = -1.0;    // -1.0 indicates to use CVar default
}

public void OnClientPutInServer(int client)
{
    // Check initial style when player is fully in server
    if (IsClientInGame(client) && !IsFakeClient(client))
    {
        CheckAndSetOptimizerForStyle(client, Shavit_GetBhopStyle(client));
    }
}

public void Shavit_OnStyleChanged(int client, int oldstyle, int newstyle)
{
    CheckAndSetOptimizerForStyle(client, newstyle);
}

void CheckAndSetOptimizerForStyle(int client, int styleId)
{
    if (client == 0 || !IsClientInGame(client))
    {
        return;
    }
    // LogMessage("[OptiDebug] Client %d (%N): CheckAndSetOptimizerForStyle called with styleId: %d", client, client, styleId);

    // Using Shavit_GetStyleSettingBool
    // This requires a corresponding boolean key in your style config (e.g., "s4f_optimizer_enabled": "1")

    g_bOptimizerEnabled[client] = Shavit_GetStyleSettingBool(styleId, "s4f_optimizer_enabled");
    // LogMessage("[OptiDebug] Client %d: Style %d, s4f_optimizer_enabled: %s", client, styleId, g_bOptimizerEnabled[client] ? "Yes" : "No");

    // LogMessage("[OptiDebug] Client %d: g_bOptimizerEnabled set to %s", client, g_bOptimizerEnabled[client] ? "true" : "false");

    // Reset last yaw if optimizer is now disabled (either because style doesn't have it, or GetStyleStrings failed)
    if (!g_bOptimizerEnabled[client])
    {
        g_flLastYaw[client] = 0.0;
    }
}

public Action Command_SetOptimizerStrength(int client, int args)
{
    if (client == 0)
    {
        PrintToServer("This command can only be used by players.");
        return Plugin_Handled;
    }
    if (args < 1)
    {
        PrintToChat(client, "Usage: sm_optimizestrength <0.1-1.0>");
        return Plugin_Handled;
    }
    char sStrength[16];
    GetCmdArg(1, sStrength, sizeof(sStrength));
    float strength = StringToFloat(sStrength);
    if (strength < 0.1 || strength > 1.0)
    {
        PrintToChat(client, "Strength must be between 0.1 and 1.0.");
        return Plugin_Handled;
    }
    g_fOptimizerStrength[client] = strength;
    PrintToChat(client, "[Optimizer] Strafe Optimizer strength set to %.2f", strength);
    return Plugin_Handled;
}

public Action Command_SetOptimizerMouseDelta(int client, int args)
{
    if (client == 0)
    {
        PrintToServer("This command can only be used by players.");
        return Plugin_Handled;
    }
    if (args < 1)
    {
        PrintToChat(client, "Usage: sm_optimousedelta <0.0-5.0>. Current: %.2f (Use -1 to reset to server default: %.2f)", g_fPlayerMinMouseDelta[client] == -1.0 ? g_cvOptimizerMinMouseDelta.FloatValue : g_fPlayerMinMouseDelta[client], g_cvOptimizerMinMouseDelta.FloatValue);
        return Plugin_Handled;
    }
    char sArg[16];
    GetCmdArg(1, sArg, sizeof(sArg));
    float value = StringToFloat(sArg);
    if (value == -1.0)
    {
        g_fPlayerMinMouseDelta[client] = -1.0;
        PrintToChat(client, "[Optimizer] Mouse delta reset to server default (%.2f).", g_cvOptimizerMinMouseDelta.FloatValue);
    }
    else if (value < 0.0 || value > 5.0) {
        PrintToChat(client, "Mouse delta must be between 0.0 and 5.0, or -1 to reset.");
        return Plugin_Handled;
    }
    else {
        g_fPlayerMinMouseDelta[client] = value;
        PrintToChat(client, "[Optimizer] Mouse delta set to %.2f.", value);
    }
    return Plugin_Handled;
}

public Action Command_SetOptimizerActivationFactor(int client, int args)
{
    if (client == 0)
    {
        PrintToServer("This command can only be used by players.");
        return Plugin_Handled;
    }
    if (args < 1)
    {
        PrintToChat(client, "Usage: sm_optifactor <0.0-1.0>. Current: %.2f (Use -1 to reset to server default: %.2f)", g_fPlayerActivationFactor[client] == -1.0 ? g_cvOptimizerActivationFactor.FloatValue : g_fPlayerActivationFactor[client], g_cvOptimizerActivationFactor.FloatValue);
        return Plugin_Handled;
    }
    char sArg[16];
    GetCmdArg(1, sArg, sizeof(sArg));
    float value = StringToFloat(sArg);
    if (value == -1.0)
    {
        g_fPlayerActivationFactor[client] = -1.0;
        PrintToChat(client, "[Optimizer] Activation factor reset to server default (%.2f).", g_cvOptimizerActivationFactor.FloatValue);
    }
    else if (value < 0.0 || value > 1.0) {
        PrintToChat(client, "Activation factor must be between 0.0 and 1.0, or -1 to reset.");
    }
    else {
        g_fPlayerActivationFactor[client] = value;
        PrintToChat(client, "[Optimizer] Activation factor set to %.2f.", value);
    }
    return Plugin_Handled;
}

public Action OnPlayerRunCmd(int client, int& buttons, int& impulse, float vel[3], float angles[3], int& weapon, int& subtype, int& cmdnum, int& tickcount, int& seed, int mouse[2])
{
    // --- Debug Start ---
    // LogMessage("[OptiDebugRunCmd] Client %d (%N) Tick %d: OptiStyle=%s | RawAngY=%.2f, LastYaw=%.2f", client, client, tickcount, g_bOptimizerEnabled[client] ? "T":"F", angles[1], g_flLastYaw[client]);

    // --- Strafe Optimizer Logic (Uses potentially smoothed angles[1]) ---
    float flCurrentPlayerViewYaw = angles[1];

    if (!g_bOptimizerEnabled[client] ||                                                                                                                                            // Optimizer specific check
        !IsPlayerAlive(client) || GetEntityFlags(client) & FL_ONGROUND || GetEntityMoveType(client) != MOVETYPE_WALK || !(GetEntProp(client, Prop_Data, "m_nWaterLevel") <= 1))    // Not in water
    {
        g_flLastYaw[client] = flCurrentPlayerViewYaw;
        // LogMessage("[OptiDebugRunCmd] Client %d: Optimizer pre-checks failed or not enabled. Returning Continue.", client);

        return Plugin_Continue;
    }

    // Only optimize if A or D is pressed, but not both, and not W/S for simplicity now.
    bool press_a = !!(buttons & IN_MOVELEFT);
    bool press_d = !!(buttons & IN_MOVERIGHT);
    // bool press_w = buttons & IN_FORWARD;
    // bool press_s = buttons & IN_BACK;

    if (!press_a && !press_d || flSpeed2D < 1.0)
        return Plugin_Continue;

    // Calculate mouse delta for this tick
    float flMouseDeltaYaw     = AngleNormalize(flCurrentPlayerViewYaw - g_flLastYaw[client]);
    float flMinMouseDeltaCvar = (g_fPlayerMinMouseDelta[client] == -1.0) ? g_cvOptimizerMinMouseDelta.FloatValue : g_fPlayerMinMouseDelta[client];

    if (flMinMouseDeltaCvar > 0.0 && FloatAbs(flMouseDeltaYaw) < flMinMouseDeltaCvar)
    {
        // Player isn't actively moving mouse enough based on the basic threshold
        g_flLastYaw[client] = flCurrentPlayerViewYaw;
        // LogMessage("[OptiDebugRunCmd] Client %d: Mouse delta too small (%.2f < %.2f). Returning Continue.", client, FloatAbs(flMouseDeltaYaw), flMinMouseDeltaCvar);
        return Plugin_Continue;
    }

    float flAirAccelerate = g_cvAirAccelerate.FloatValue;
    int   style           = Shavit_GetBhopStyle(client);
    float flStyleRunSpeed = Shavit_GetStyleSettingFloat(style, "runspeed");
    if (flStyleRunSpeed <= 0.0) flStyleRunSpeed = 260.0;    // Fallback if style runspeed is not set

    float flSurfaceFriction = 1.0;
    if (g_iSurfaceFrictionOffset > 0)
    {
        flSurfaceFriction = GetEntDataFloat(client, g_iSurfaceFrictionOffset);
    }

    float flTickInterval = GetTickInterval();

    float vecPlayerVelocity[3];
    GetEntPropVector(client, Prop_Data, "m_vecVelocity", vecPlayerVelocity);

    float vecPlayerVelocity2D[2];
    vecPlayerVelocity2D[0] = vecPlayerVelocity[0];
    vecPlayerVelocity2D[1] = vecPlayerVelocity[1];

    float flSpeed2D        = GetVectorLength(vecPlayerVelocity, true);    // Pass the 3D vector, true for 2D length
    if (flSpeed2D < 1.0)                                                  // Avoid issues at very low speeds
    {
        g_flLastYaw[client] = flCurrentPlayerViewYaw;
        // LogMessage("[OptiDebugRunCmd] Client %d: Speed too low (%.2f). Returning Continue.", client, flSpeed2D);

        return Plugin_Continue;
    }

    float flYawVelocity   = Vec2DToYaw(vecPlayerVelocity2D);
    float flTheta         = GetThetaAngleInAir(vecPlayerVelocity2D, flAirAccelerate, flStyleRunSpeed, flSurfaceFriction, flTickInterval, g_flAirSpeedCap);

    float flTargetViewYaw = angles[1];
    bool  bShouldOptimize = false;

    if (press_a)    // Player presses A (wants to strafe left relative to view)
    {
        flTargetViewYaw = AngleNormalize(flYawVelocity - flTheta + 90.0);
        bShouldOptimize = true;
    }
    else if (press_d)    // Player presses D (wants to strafe right relative to view)
    {
        flTargetViewYaw = AngleNormalize(flYawVelocity + flTheta - 90.0);
        bShouldOptimize = true;
    }

    if (bShouldOptimize)
    {
        // Additional activation condition based on cheat.cpp inspiration
        // This check is performed IF the optimizer is otherwise inclined to run (A/D pressed, min mouse delta met, etc.)
        float flActivationFactor = (g_fPlayerActivationFactor[client] == -1.0) ? g_cvOptimizerActivationFactor.FloatValue : g_fPlayerActivationFactor[client];
        if (flActivationFactor > 0.0)
        {
            float flPlayerActualTurnMag           = FloatAbs(flMouseDeltaYaw);
            // How far was the player's view at the START of this tick's input processing from the ideal angle calculated FOR this tick?
            float flAngleToTargetFromLastTickView = AngleNormalize(flTargetViewYaw - g_flLastYaw[client]);
            float flNeededTurnMag                 = FloatAbs(flAngleToTargetFromLastTickView);

            // If the player's actual turn is less than a factor of the "needed" turn, and the "needed" turn isn't tiny (already optimal)
            if (flPlayerActualTurnMag < (flNeededTurnMag * flActivationFactor) && flNeededTurnMag > 0.5 /* Epsilon to avoid issues if already near optimal */)
            {
                g_flLastYaw[client] = flCurrentPlayerViewYaw;    // Store unmodified yaw, as we're bailing
                // LogMessage("[OptiDebugRunCmd] Client %d: Activation factor check failed (ActualTurn %.2f < NeededTurn %.2f * Factor %.2f). Returning Continue.", client, flPlayerActualTurnMag, flNeededTurnMag, flActivationFactor);
                return Plugin_Continue;    // Don't optimize if player's turn isn't "aggressive" enough towards the optimal
            }
        }

        float flEffectiveStrength = g_fOptimizerStrength[client];

        // Use flCurrentPlayerViewYaw (player's raw input for the tick) as the base for applying the difference
        float flYawDifference     = AngleNormalize(flTargetViewYaw - flCurrentPlayerViewYaw);
        angles[1]                 = AngleNormalize(flCurrentPlayerViewYaw + flYawDifference * flEffectiveStrength);

        // vel[0] = 0.0; // Optional: Clear forwardmove to prevent interference. Test this.
        g_flLastYaw[client]       = angles[1];    // Store the MODIFIED yaw for the next tick's delta calculation
        // LogMessage("[OptiDebugRunCmd] Client %d: Optimizer APPLIED. New Angle: %.2f. Returning Plugin_Changed.", client, angles[1]);
        return Plugin_Changed;
    }

    g_flLastYaw[client] = flCurrentPlayerViewYaw;    // Store the UNMODIFIED (original) yaw
    // LogMessage("[OptiDebugRunCmd] Client %d: Optimizer did not apply. Returning Continue.", client);
    return Plugin_Continue;
}

stock float Clamp(float val, float min, float max)
{
    if (val < min) return min;
    if (val > max) return max;
    return val;
}

stock float DegreesToRadians(float degrees)
{
    return degrees * (FLOAT_PI / 180.0);
}

stock float RadiansToDegrees(float radians)
{
    return radians * (180.0 / FLOAT_PI);
}

public Action Command_CheckOptiStatus(int client, int args)
{
    if (client == 0) return Plugin_Handled;    // Console can't use this effectively without a target

    int target = client;    // Default to self
    if (args >= 1)
    {
        char sTargetArg[64];
        GetCmdArg(1, sTargetArg, sizeof(sTargetArg));
        target = FindTarget(client, sTargetArg, false, false);    // Set last arg to false if you don't want to allow partial names
        if (target <= 0 || !IsClientInGame(target))
        {
            PrintToChat(client, "[Optimizer] Player not found or not in game.");
            return Plugin_Handled;
        }
    }

    PrintToChat(client, "[Optimizer] Status for %N:", target);
    PrintToChat(client, "  Style ID: %d | OptiEnabled: %s ", Shavit_GetBhopStyle(target), g_bOptimizerEnabled[target] ? "Yes" : "No");
    PrintToChat(client, "  OptiStrength: %.2f | LastYaw: %.2f", g_fOptimizerStrength[target], g_flLastYaw[target]);
    PrintToChat(client, "  MinMouseDelta: %.2f (Server: %.2f) | ActivationFactor: %.2f (Server: %.2f)", (g_fPlayerMinMouseDelta[target] == -1.0 ? g_cvOptimizerMinMouseDelta.FloatValue : g_fPlayerMinMouseDelta[target]), g_cvOptimizerMinMouseDelta.FloatValue, (g_fPlayerActivationFactor[target] == -1.0 ? g_cvOptimizerActivationFactor.FloatValue : g_fPlayerActivationFactor[target]), g_cvOptimizerActivationFactor.FloatValue);
    return Plugin_Handled;
}