#pragma semicolon 1
#pragma newdecls required

#include <sourcemod>
#include <sdktools>    // For ForceChangeLevel, GetCurrentMap, IsMapRunning etc.
#include <shavit>      // For ReadMapList

// Include sh-servers as optional. The plugin will check for its availability.
#undef REQUIRE_PLUGIN
#include <sh-servers>

#define PLUGIN_VERSION "1.0"

public Plugin myinfo =
{
    name        = "S4F - Unique Map",
    author      = "Proxychains",
    description = "Forces map change if current map is active on another server (uses sh-servers API).",
    version     = PLUGIN_VERSION,
    url         = "https://surfing4.fun"
};

// Globals
ArrayList g_alBlockedMapsByOtherServers = null;    // Stores names of maps active on other servers
bool      g_bShServersAvailable         = false;

public void OnPluginStart()
{
    g_alBlockedMapsByOtherServers = new ArrayList(PLATFORM_MAX_PATH);

    RegPluginLibrary("s4f-uniquemap");    // Library name for the native
    CreateNative("UniqueMap_IsMapBlocked", Native_IsMapBlocked);

    // Check initial availability of sh-servers
    if (LibraryExists("sh-servers"))
    {
        g_bShServersAvailable = true;
        LogMessage("[UniqueMap] sh-servers library found on startup.");
        // Attempt an initial update if sh-servers might already have data
        RequestFrame(UpdateBlockedMapsListFrame);
    }
    else {
        LogMessage("[UniqueMap] sh-servers library NOT found on startup. Waiting for OnLibraryAdded.");
    }

    RegAdminCmd("sm_printblockedmaps", Command_PrintBlockedMaps, ADMFLAG_ROOT, "Prints the current list of maps considered blocked.");
}

public void OnPluginEnd()
{
    if (g_alBlockedMapsByOtherServers != null)
    {
        delete g_alBlockedMapsByOtherServers;
        g_alBlockedMapsByOtherServers = null;
    }
}

public void OnConfigsExecuted()
{
    // Attempt to update the list if sh-servers is available
    UpdateBlockedMapsList();
}

void UpdateBlockedMapsListFrame(any data)
{
    UpdateBlockedMapsList();
}

void UpdateBlockedMapsList()
{
    if (!g_bShServersAvailable || g_alBlockedMapsByOtherServers == null)
    {
        // LogMessage("[UniqueMap] Cannot update blocked maps list: sh-servers not available or list not initialized.");
        return;
    }

    g_alBlockedMapsByOtherServers.Clear();

    ConVar cvHostPort      = FindConVar("hostport");
    int    currentHostPort = cvHostPort ? cvHostPort.IntValue : 0;

    // LogMessage("[UniqueMap] Updating blocked maps list. Server count from sh-servers: %d. Current Port: %d", g_iServerCount, currentHostPort);

    for (int i = 0; i < g_iServerCount; i++)    // g_iServerCount is from sh-servers.inc
    {
        // Check if this entry is the current server by comparing ports
        char sServerAddr[32];
        strcopy(sServerAddr, sizeof(sServerAddr), g_sAddress[i]);    // g_sAddress is from sh-servers.inc
        char sAddrParts[2][32];
        bool isCurrentServer = false;
        if (ExplodeString(sServerAddr, ":", sAddrParts, 2, 32) == 2)
        {
            int serverPort = StringToInt(sAddrParts[1]);
            if (serverPort == currentHostPort)
            {
                isCurrentServer = true;
            }
        }

        if (isCurrentServer)
        {
            // LogMessage("[UniqueMap] Skipping self in server list: %s on %s", g_sMapName[i], g_sAddress[i]);
            continue;    // Skip current server
        }

        char sMapOnOtherServer[PLATFORM_MAX_PATH];
        strcopy(sMapOnOtherServer, sizeof(sMapOnOtherServer), g_sMapName[i]);    // g_sMapName is from sh-servers.inc
        LowercaseString(sMapOnOtherServer);                                      // Normalize to lowercase for consistent checks

        if (sMapOnOtherServer[0] != '\0' && !StrEqual(sMapOnOtherServer, "lobby", false) && StrContains(sMapOnOtherServer, "unknown", false) == -1)
        {
            if (g_alBlockedMapsByOtherServers.FindString(sMapOnOtherServer) == -1)
            {
                g_alBlockedMapsByOtherServers.PushString(sMapOnOtherServer);
                LogMessage("[UniqueMap] Map '%s' on server %s (%s) is now considered blocked.", sMapOnOtherServer, g_sServerName[i], g_sAddress[i]);
            }
        }
    }
    // LogMessage("[UniqueMap] Blocked maps list updated. Count: %d", g_alBlockedMapsByOtherServers.Length);
}

// --- sh-servers interaction ---
public void OnLibraryAdded(const char[] name)
{
    if (StrEqual(name, "sh-servers"))
    {
        LogMessage("[UniqueMap] sh-servers library dynamically loaded.");
        g_bShServersAvailable = true;
        // Update the list as sh-servers is now available
        UpdateBlockedMapsList();
    }
}

public void OnLibraryRemoved(const char[] name)
{
    if (StrEqual(name, "sh-servers"))
    {
        LogMessage("[UniqueMap] sh-servers library unloaded.");
        g_bShServersAvailable = false;
        if (g_alBlockedMapsByOtherServers != null)
        {
            g_alBlockedMapsByOtherServers.Clear();
            LogMessage("[UniqueMap] Blocked maps list cleared as sh-servers unloaded.");
        }
    }
}

/**
 * Called when the server list from sh-servers is updated.
 * This forward is declared in sh-servers.inc.
 */
public void OnServerListUpdated(int serverCount)
{
    // This is the primary trigger to update our blocked maps list
    if (!g_bShServersAvailable)    // Ensure flag is set if this is the first sign of sh-servers
    {
        g_bShServersAvailable = true;
    }
    // LogMessage("[UniqueMap] Server list updated (count: %d). Re-evaluating blocked maps.", serverCount);
    UpdateBlockedMapsList();
}

// --- Native Implementation ---
public int Native_IsMapBlocked(Handle plugin, int numParams)
{
    char mapName[PLATFORM_MAX_PATH];
    GetNativeString(1, mapName, sizeof(mapName));
    // Map name from mapchooser is already lowercase.

    if (g_alBlockedMapsByOtherServers == null) return false;

    return g_alBlockedMapsByOtherServers.FindString(mapName) != -1;
}

// --- Test Command ---
public Action Command_PrintBlockedMaps(int client, int args)
{
    if (g_alBlockedMapsByOtherServers == null || g_alBlockedMapsByOtherServers.Length == 0)
    {
        ReplyToCommand(client, "[UniqueMap] No maps are currently considered blocked.");
        return Plugin_Handled;
    }

    ReplyToCommand(client, "[UniqueMap] Maps currently blocked (active on other servers):");
    for (int i = 0; i < g_alBlockedMapsByOtherServers.Length; i++)
    {
        char sMapName[PLATFORM_MAX_PATH];
        g_alBlockedMapsByOtherServers.GetString(i, sMapName, sizeof(sMapName));
        ReplyToCommand(client, "- %s", sMapName);
    }
    return Plugin_Handled;
}
