#include <sourcemod>
#include <sdktools>

#pragma newdecls required

public void OnPluginStart()
{
    HookEvent("player_spawn", Event_PlayerSpawn, EventHookMode_Post);
}

public Action Event_PlayerSpawn(Event event, const char[] name, bool dontBroadcast)
{
    int client = GetClientOfUserId(GetEventInt(event, "userid"));

    if (IsClientInGame(client) && IsPlayerAlive(client))
    {
        // Loop through weapon slots 0 (primary), 1 (secondary), 2 (knife), 3 (grenades)
        for (int i = 0; i < 4; i++)
        {
            int ent;
            while ((ent = GetPlayerWeaponSlot(client, i)) != -1)
            {
                RemovePlayerItem(client, ent);
                AcceptEntityInput(ent, "Kill");    // Remove the weapon entity from the world
            }
        }

        // GivePlayerItem(client, "weapon_knife");
    }
    return Plugin_Continue;
}