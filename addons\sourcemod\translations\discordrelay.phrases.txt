"Phrases"
{
    "Discord To Server"
    {
        "#format"    "{1:s},{2:s}"  // {1} = Discord Username | {2} = Message
        "en"    "*DISCORD* {gray}{1}{default} :  {2}"
    }
    "Discord To Server Reply"
    {
        "#format"    "{1:s},{2:s}"  // {1} = Discord Username | {2} = Message
        "en"    "*DISCORD* {gray}(Reply) {1}{default} :  {2}"
    }
    "Player Join"
    {
        "#format"    "{1:s},{2:s}"  // {1} = Player Name | {2} = SteamID
        "en"    "[{1}](http://www.steamcommunity.com/profiles/{2}) joined the game"
    }
    "Player Banned"
    {
        "#format"    "{1:s},{2:s}"
        "en"    "[{1}](http://www.steamcommunity.com/profiles/{2}) has been banned"
    }
    "Player Leave"
    {
        "#format"    "{1:s},{2:s}"
        "en"    "[{1}](http://www.steamcommunity.com/profiles/{2}) left the game"
    }
    "Player Report"
    {
        "#format"    "{1:s},{2:s}"  // {1} = Player Name | {2} = SteamID
        "en"    "[{1}](http://www.steamcommunity.com/profiles/{2}) has submitted a report!"
    }
    "Player Report Title"
    {
        "en"    "Player Report"
    }
    "Player Report Reason"
    {
        "en"    "Reason:"
    }
    "Hibernation Enter"
    {
        "en"    "Server is currently empty!"
    }
    "Hibernation Exit"
    {
        "en"    "Someone is joining!"
    }
    "Chat Relay Stopped"
    {
        "en"    "Chat relay stopped!"
    }
    "RCON Relay Stopped"
    {
        "en"    "RCON commands relay stopped!"
    }
    "Listening to Chat"
    {
        "en"    "Listening to chat messages..."
    }
    "Listening to RCON"
    {
        "en"    "Listening to RCON commands..."
    }
    "Current Map"
    {
        "en"    "Current Map:"
    }
    "Previous Map"
    {
        "en"    "Previous Map:"
    }
    "Map Name"
    {
        "#format"    "{1:s},{2:s}"  // {1} = Map Name | {2} = Map URL
        "en"    "[{1}]({2})"
    }
    "Player Count"
    {
        "en"    "Player Count:"
    }
    "Server Name"
    {
        "en"    "Name:"
    }
    "Server Tags"
    {
        "en"    "Tags:"
    }
    "RCON Print Error"
    {
        // ```diff\n
        // -Unable to print command response-\n
        // ```
        "en"    "```diff\n-Unable to print command response-\n```"  // Use '\n' for new lines
    }
    "RCON Output"
    {
        // ```<programming_language>\n
        // <output>\n
        // ```
        "#format"    "{1:s}"
        "en"    "```dsconfig\n{1}\n```"  // Use '\n' for new lines
    }
    "RCON Input"
    {
        // ```<programming_language>\n
        // <input>\n
        // ```
        "#format"    "{1:s}"
        "en"    "```hs\n{1}\n```"  // Use '\n' for new lines
    }
    "Blocked Message Title"
    {
        "#format"    "{1:s},{2:s}"
        "en"    "**Blocked message from [{1}](http://www.steamcommunity.com/profiles/{2})**"
}}