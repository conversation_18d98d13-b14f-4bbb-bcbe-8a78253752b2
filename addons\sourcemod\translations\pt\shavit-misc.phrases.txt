"Phrases"
{
	// ---------- Commands ---------- //
	"CommandAlive"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Você precisa estar {1}vivo{2} para usar este comando."
	}
	"CommandAliveSpectate"
	{
		"#format"	"{1:s},{2:s},{3:s},{4:s}"
		"pt"		"Você precisa estar {1}vivo{2} ou {3}espectando um jogador{4} para usar este comando."
	}
	"CommandSaveCPKZInvalid"
	{
		"pt"		"Você não pode realizar esta ação enquanto estiver no ar ou observando outro jogador."
	}
	"CommandSaveCPKZZone"
	{
		"pt"		"Você não pode realizar esta ação enquanto estiver na zona de início."
	}
	"CommandTeleCPInvalid"
	{
		"pt"		"Você não pode teleportar para este checkpoint devido a uma incompatibilidade de configurações."
	}
	"CommandNoPause"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Seu timer precisa estar {1}retomado{2} para usar este comando."
	}
	"CommandDisabled"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Este comando está {1}desativado{2}."
	}
	"FeatureDisabled"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Esta funcionalidade está {1}desativada{2}."
	}
	"HideEnabled"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Você agora está {1}ocultando{2} jogadores."
	}
	"HideDisabled"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Você agora {1}não está ocultando{2} jogadores."
	}
	"AutoRestartEnabled"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Você agora irá {1}reiniciar automaticamente{2} se for mais lento que seu PB."
	}
	"AutoRestartDisabled"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Você não irá mais {1}reiniciar automaticamente{2}."
	}
	"AutoRestartTriggered1"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Você foi {1}reiniciado automaticamente{2} por ser mais lento que seu PB."
	}
	"AutoRestartTriggered2"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Use {1}!autorestart{2} para desativar isso."
	}
	"LackingAccess"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Você {1}não tem acesso{2} a este comando."
	}
	"SpectateDead"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Você {1}não pode{2} mirar em um jogador morto."
	}
	"SpectatorCount"
	{
		"#format"	"{1:s},{2:N},{3:s},{4:s},{5:i},{6:s},{7:s}"
		"pt"		"{1}{2}{3} tem {4}{5}{6} espectadores: {7}"
	}
	"SpectatorCountZero"
	{
		"#format"	"{1:s},{2:N},{3:s}"
		"pt"		"Ninguém está espectando {1}{2}{3}."
	}
	"SpectatorInvalid"
	{
		"pt"		"Você deve estar vivo ou espectar alguém para ver seus/deles espectadores."
	}
	"TeleportAlive"
	{
		"pt"		"Você só pode teleportar se estiver vivo."
	}
	"TeleportInvalidTarget"
	{
		"pt"		"Alvo inválido."
	}
	"MiscCheckpointsSaved"
	{
		"#format"	"{1:s},{2:d},{3:s}"
		"pt"		"Checkpoint ({1}{2}{3}) salvo."
	}
	"MiscCheckpointsTeleported"
	{
		"#format"	"{1:s},{2:d},{3:s}"
		"pt"		"Teleportado para o checkpoint ({1}{2}{3})."
	}
	"MiscCheckpointsEmpty"
	{
		"#format"	"{1:d},{2:s},{3:s}"
		"pt"		"Checkpoint {1} está {2}vazio{3}."
	}
	"MiscCheckpointsOverflow"
	{
		"pt"		"Não foi possível salvar devido ao excesso de checkpoints."
	}
	"MiscCheckpointOwnerInvalid"
	{
		"pt"		"O proprietário do checkpoint é inválido ou desconectou-se."
	}
	"MiscSegmentedCommand"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Use {1}!cp{2} para reabrir o menu de checkpoints."
	}
	// ---------- Menus ---------- //
	"StopTimerWarning"
	{
		"pt"		"Tem certeza que quer parar seu timer?"
	}
	"StopTimerYes"
	{
		"pt"		"Sim, pare meu timer."
	}
	"StopTimerNo"
	{
		"pt"		"Não, mantenha meu timer rodando."
	}
	"ClearCPWarning"
	{
		"pt"		"Tem certeza que quer limpar seus checkpoints?"
	}
	"ClearCPYes"
	{
		"pt"		"Sim, e eu sei que esta ação não pode ser revertida."
	}
	"ClearCPNo"
	{
		"pt"		"Não."
	}
	"MiscCheckpointNoOtherPlayers"
	{
		"pt"		"Você está sozinho (além disso, o servidor está vazio)"
	}
	"TeleportMenuTitle"
	{
		"pt"		"Teleportar para:"
	}
	"WeaponAlive"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Você precisa estar {1}vivo{2} para gerar armas."
	}
	"MiscCheckpointMenu"
	{
		"pt"		"Checkpoints"
	}
	"MiscCheckpointMenuSegmented"
	{
		"pt"		"Checkpoints Segmentados"
	}
	"MiscCheckpointWarning"
	{
		"pt"		"AVISO: Teleportar irá parar seu timer!"
	}
	"MiscCheckpointSave"
	{
		"#format"	"{1:d},{2:d}"
		"pt"		"Salvar checkpoint ({1}/{2})"
	}
	"MiscCheckpointDuplicate"
	{
		"#format"	"{1:d},{2:d}"
		"pt"		"Duplicar checkpoint ({1}/{2})"
	}
	"MiscCheckpointTeleport"
	{
		"#format"	"{1:d}"
		"pt"		"Teleportar para checkpoint ({1})"
	}
	"MiscCheckpointPrevious"
	{
		"pt"		"Anterior"
	}
	"MiscCheckpointNext"
	{
		"pt"		"Próximo"
	}
	"MiscCheckpointUseOthers"
	{
		"pt"		"Usar checkpoints de outro jogador"
	}
	"MiscCheckpointBack"
	{
		"pt"		"Voltar para seus checkpoints"
	}
	"MiscCheckpointReset"
	{
		"pt"		"Resetar checkpoints"
	}
	"MiscCheckpointDeleteCurrent"
	{
		"pt"		"Excluir checkpoint atual"
	}
	"MiscCheckpointPause"
	{
		"pt"		"Pausar"
	}
	"MiscCheckpointUseAngles"
	{
		"pt"		"Usar ângulos"
	}
	"MiscCheckpointUseVelocity"
	{
		"pt"		"Usar velocidade"
	}
	"TasSettings"
	{
		"pt"        "Configurações de TAS"
	}
	"Autostrafer"
	{
		"pt"        "Autostrafer"
	}
	"AutoJumpOnStart"
	{
		"pt"        "Pular na saída da zona de início"
	}
	"EdgeJump"
	{
		"pt"        "Pulo na borda"
	}
	"AutogainBasicStrafer"
	{
		"pt"        "Strafer básico com autoganho"
	}
	"AutoPrestrafe"
	{
		"pt"        "Auto Prestrafe"
	}
	"Autostrafer_type"
	{
		"pt"        "Tipo"
	}
	"Autostrafer_1tick"
	{
		"pt"        "1Tick (xutaxkamay)"
	}
	"Autostrafer_autogain"
	{
		"pt"        "Velocidade/autoganho (oblivious)"
	}
	"Autostrafer_autogain_nsl"
	{
		"pt"        "Velocidade/autoganho (Sem perda de velocidade) (oblivious)"
	}
	"Autostrafer_basic"
	{
		"pt"		"Básico"
	}
	"TASEnabled"
	{
		"pt"        "Ativado"
	}
	"TASDisabled"
	{
		"pt"        "Desativado"
	}
	"AutostrafeOverride"
	{
		"pt"        "Substituição de Tecla"
	}
	"AutostrafeOverride_Normal"
	{
		"pt"        "W/S"
	}
	"AutostrafeOverride_Surf"
	{
		"pt"        "W/S (e A/D em rampas de surf)"
	}
	"AutostrafeOverride_Surf_W_Okay"
	{
		"pt"        "S (e A/D em rampas de surf)"
	}
	"AutostrafeOverride_All"
	{
		"pt"        "W/S A/D"
	}
	// ---------- Misc ---------- //
	"BHStartZoneDisallowed"
	{
		"#format"	"{1:s},{2:s},{3:s},{4:s}"
		"pt"		"Bunnyhopping na {1}zona de início{2} {3}não é permitido{4}."
	}
	"WRNotice"
	{
		"#format"	"{1:s},{2:s},{3:s}"
		"pt"		"{1}{2}{3} estabeleceu um novo recorde no mapa!"
	}
	"WRNoticeStage"
	{
		"#format"	"{1:s},{2:s},{3:s},{4:s},{5:s},{6:s}"
		"pt"		"{1}{2}{3} estabeleceu um novo recorde no estágio [{4}{5}{6}]!"
	}
	"WRNoticeBonus"
	{
		"#format"	"{1:s},{2:s},{3:s},{4:s},{5:s},{6:s}"
		"pt"		"{1}{2}{3} estabeleceu um novo recorde no bônus [{4}{5}{6}]!"
	}
	"AdvertisementsEnabled"
	{
		"pt"		"Anúncios ativados"
	}
	"AdvertisementsDisabled"
	{
		"pt"		"Anúncios desativados"
	}
	"PauseNotOnGround"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Você {1}não tem permissão{2} para usar noclip quando não estiver no chão."
	}
	"PauseMoving"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Você {1}não tem permissão{2} para usar noclip enquanto se move."
	}
	"PauseDuck"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Você {1}não tem permissão{2} para usar noclip enquanto está agachado."
	}
	"MessagePause"
	{
		"#format"	"{1:s},{2:s},{3:s}"
		"pt"		"{1}O timer foi {2}pausado{3}."
	}
	"MenuTitle_Command"
	{
		"pt"		"Menu de Comandos"
	}
}
