#if defined _s4f_uniquemap_natives_included
    #endinput
#endif
#define _s4f_uniquemap_natives_included

/**
 * Checks if a map is currently considered "blocked" because it's active on another server.
 * Requires the s4f-uniquemap plugin to be running and sh-servers to be providing data.
 *
 * @param mapName       The name of the map to check (expected to be lowercase).
 * @return              True if the map is blocked, false otherwise or if s4f-uniquemap is not available.
 */
native bool UniqueMap_IsMapBlocked(const char[] mapName);

#if !defined REQUIRE_PLUGIN
public void __pl_s4f_uniquemap_SetNTVOptional() // Ensure library name matches RegPluginLibrary
{
    MarkNativeAsOptional("UniqueMap_IsMapBlocked");
}
#endif