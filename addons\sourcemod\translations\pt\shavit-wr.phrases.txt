"Phrases"
{
	// ---------- Commands ---------- //
	"DeletionAborted"
	{
		"pt"		"Exclusão abortada."
	}
	"DeletedRecord"
	{
		"pt"		"Registro excluído."
	}
	"DeletedRecordsMap"
	{
		"#format"	"{1:s},{2:s},{3:s}"
		"pt"		"TODOS os registros para {1}{2}{3} foram excluídos."
	}
	"DeletedRecordsStyle"
	{
		"#format"	"{1:s},{2:s},{3:s}"
		"pt"		"TODOS os registros para {1}{2}{3} foram excluídos."
	}
	// ---------- Client Menus ---------- //
	"ListClientRecords"
	{
		"#format"	"{1:s},{2:s},{3:s}"
		"pt"		"Registros para {1}: [{2}]\n({3})"
	}
	"ListPersonalBest"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Melhor pessoal para {1} em {2}"
	}
	// ---------- Completion Messages ---------- //
	"ServerFirstCompletion"
	{
		"#format"	"{1:s},{2:s},{3:s},{4:s},{5:s},{6:s},{7:s},{8:s},{9:s},{10:s},{11:d},{12:s},{13:s},{14:d},{15:s},{16:s},{17:s},{18:s}"
		"pt"		"{1}{2}{3} terminou [{4}{5}{6}] em {7}{8}{9}. Rank: {10}{11}{12}/{13}{14}{15} ({16}{17}{18})"
	}
	"PlayerFirstCompletion"
	{
		"#format"	"{1:s},{2:s},{3:s},{4:s},{5:s},{6:s},{7:s},{8:s},{9:s},{10:s},{11:s},{12:d},{13:s},{14:s},{15:d},{16:s},{17:s},{18:s},{19:s}"
		"pt"		"{1}{2}{3} terminou [{4}{5}{6}] em {7}{8}{9} (SR: {10}). Rank: {11}{12}{13}/{14}{15}{16} ({17}{18}{19})"
	}
	"NotFirstCompletion"
	{
		"#format"	"{1:s},{2:s},{3:s},{4:s},{5:s},{6:s},{7:s},{8:s},{9:s},{10:s},{11:s},{12:s},{13:d},{14:s},{15:s},{16:d},{17:s},{18:s},{19:s},{20:s}"
		"pt"		"{1}{2}{3} terminou [{4}{5}{6}] em {7}{8}{9} (SR: {10} | PB: {11}). Rank: {12}{13}{14}/{15}{16}{17} ({18}{19}{20})"
	}
	"NotFirstCompletionWorse"
	{
		"#format"	"{1:s},{2:s},{3:s},{4:s},{5:s},{6:s},{7:s},{8:s},{9:s},{10:s},{11:s},{12:s},{13:s},{14:s}"
		"pt"		"{1}{2}{3} terminou [{4}{5}{6}] em {7}{8}{9} (SR: {10} | PB: {11}). ({12}{13}{14})"
	}
	"WorseTime"
	{
		"#format"	"{1:s},{2:s},{3:s},{4:s},{5:s},{6:s},{7:s},{8:s},{9:s},{10:s},{11:s}"
		"pt"		"Você terminou [{1}{2}{3}] em {4}{5}{6} (SR: {7} | PB: {8}). ({9}{10}{11})"
	}
	"UnrankedTime"
	{
		"#format"	"{1:s},{2:s},{3:s},{4:s},{5:s},{6:s},{7:s},{8:s},{9:s}"
		"pt"		"Você terminou [{1}{2}{3}] em {4}{5}{6}. ({7}{8}{9})"
	}
	"CompletionExtraInfo"
	{
		"#format"   "{1:s},{2:.2f},{3:s},{4:s},{5:.2f},{6:s},{7:s},{8:.1f},{9:s}"
		"pt"        "Vel. Média/Máx: {1}{2}{3}/{4}{5}{6}. Perfs: {7}{8}{9}%%."
	}
	"ImprovingPointsInfo"
	{
		"#format"   "{1:s},{2:.2f},{3:s},{4:s},{5:d},{6:s},{7:s},{8:d},{9:s},{10:s},{11:s},{12:s}"
		"pt"        "Você ganhou {1}{2}{3} pontos por melhorar do Rank {4}{5}{6} para o Rank {7}{8}{9} em [{10}{11}{12}]."
	}
	"CompletionPointsInfo"
	{
		"#format"   "{1:s},{2:.2f},{3:s},{4:s},{5:s},{6:s},{7:s},{8:d},{9:s}"
		"pt"        "Você ganhou {1}{2}{3} pontos por terminar [{4}{5}{6}] no Rank {7}{8}{9}."
	}
	"ExtraFinishInfo"
	{
		"#format"   "{1:s},{2:s},{3:.f},{4:s},{5:s},{6:.f},{7:s},{8:s},{9:.f},{10:s},{11:s},{12:.f},{13:s},{14:s},{15:.2f},{16:s}"
		"pt"        "{1} Vel. Média/Máx: {2}{3}{4}/{5}{6}{7} u/s | Vel. Início/Fim: {8}{9}{10}/{11}{12}{13} u/s | Sinc: {14}{15}{16}%%"
	}
	// ---------- Deletion Menus ---------- //
	"DeleteAllRecords"
	{
		"pt"		"Excluir TODOS os recordes do mapa"
	}
	"DeleteAllStageRecords"
	{
		"pt"		"Excluir TODOS os recordes de estágio"
	}
	"DeleteAllRecordsMenuTitle"
	{
		"#format"	"{1:s},{2:s},{3:s}"
		"pt"		"Excluir TODOS os recordes para '{1}'? (Pista: {2} | Estilo: {3})"
	}
	"DeleteAllStageRecordsMenuTitle"
	{
		"#format"	"{1:s},{2:s},{3:s}"
		"pt"		"Excluir TODOS os recordes de estágio para '{1}'? (Estágio: {2} | Estilo: {3})"
	}
	"DeleteConfirm"
	{
		"pt"		"Tem certeza?"
	}
	"DeleteMenuTitle"
	{
		"pt"		"Excluir um registro de:"
	}
	"DeleteSingleRecord"
	{
		"pt"		"Excluir um único registro"
	}
	"DeleteSingleStageRecord"
	{
		"pt"		"Excluir um único registro de estágio"
	}
	"MenuResponseNo"
	{
		"pt"		"NÃO!"
	}
	"MenuResponseYesSingle"
	{
		"pt"		"SIM!!! EXCLUIR O REGISTRO!!!"
	}
	"MenuResponseYes"
	{
		"pt"		"SIM!!! EXCLUIR TODOS OS REGISTROS!!! ESTA AÇÃO É IRREVERSÍVEL!"
	}
	"DeleteStyleRecordsRecordsMenuTitle"
	{
		"#format"	"{1:s}"
		"pt"		"Escolha um estilo para excluir todos os recordes de '{1}':"
	}
	"DeleteConfirmStyle"
	{
		"#format"	"{1:s}"
		"pt"		"Tem certeza que quer excluir TODOS OS REGISTROS para {1}?"
	}
	"MenuResponseYesStyle"
	{
		"#format"	"{1:s}"
		"pt"		"SIM!!! EXCLUIR OS REGISTROS PARA {1}!!! ESTA AÇÃO É IRREVERSÍVEL!"
	}
	"DeleteTrackSingle"
	{
		"pt"		"Escolha uma pista para excluir um único registro:"
	}
	"DeleteStageSingle"
	{
		"pt"		"Escolha um estágio para excluir um único registro:"
	}
	"DeleteStageAll"
	{
		"pt"		"Escolha um estágio para excluir TODOS os registros:"
	}
	"DeleteTrackAll"
	{
		"pt"		"Escolha uma pista para excluir TODOS os registros:"
	}
	"DeleteTrackAllStyle"
	{
		"#format"	"{1:s}"
		"pt"		"Escolha um estilo para excluir todos os recordes para ({1}):"
	}
	// ---------- Errors ---------- //
	"DatabaseError"
	{
		"pt"		"Erro no banco de dados"
	}
	"Error"
	{
		"pt"		"ERRO"
	}
	"NoStyles"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"{1}ERRO FATAL: {2}Nenhum estilo está disponível. Contate o proprietário do servidor imediatamente!"
	}
	"NoPB"
	{
		"#format"	"{1:s},{2:s},{3:s},{4:s},{5:s},{6:s}"
		"pt"		"Nenhum registro de PB foi encontrado para {1}{2}{3} no mapa {4}{5}{6}."
	}
	"NoStages"
	{
		"#format"	"{1:s},{2:s},{3:s}"
		"pt"		"Não existem estágios para {1}{2}{3}."
	}
	// ---------- RR Menu ---------- //
	"RecentRecords"
	{
		"#format"	"{1:d}"
		"pt"		"{1} registro(s) recente(s)"
	}
	"RecentRecordsFirstMenuTitle"
	{
		"pt"		"Registros recentes"
	}
	"RecentRecordsStyleSelectionMenuTitle"
	{
		"pt"		"Registros recentes (por estilo)"
	}
	"RecentRecordsAll"
	{
		"pt"		"Todos os registros recentes"
	}
	"RecentRecordsByStyle"
	{
		"pt"		"Ordenado por estilo"
	}
	"RecentRecordsSelectMain"
	{
		"pt"		"Selecionar registros Principais"
	}
	"RecentRecordsSelectBonus"
	{
		"pt"		"Selecionar registros de Bônus"
	}
	"RecentRecordsSelectStage"
	{
		"pt"		"Selecionar registros de Estágio"
	}
	// ---------- WR Menu ---------- //
	"WRDate"
	{
		"pt"		"Data"
	}
	"WRJump"
	{
		"pt"		"pulo"
	}
	"WRJumps"
	{
		"pt"		"Pulos"
	}
	"WRMap"
	{
		"#format"	"{1:s}"
		"pt"		"Registros para {1}"
	}
	"WRMapNoRecords"
	{
		"pt"		"Nenhum registro encontrado."
	}
	"WRMenuTitle"
	{
		"pt"		"Escolha um estilo:\n "
	}
	"WRMenuStageTitle"
	{
		"pt"		"Escolha um estágio:\n "
	}
	"WRMenuBonusTitle"
	{
		"pt"		"Escolha um bônus:\n "
	}
	"WRPoints"
	{
		"pt"		"pontos"
	}
	"WRPlayerStats"
	{
		"pt"		"Estatísticas do jogador"
	}
	"CheckpointRecord"
	{
		"pt"		"Registros de checkpoint"
	}
	"CheckpointRecordsMenuTitle"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Registros de checkpoint para {1} em {2}"
	}
	"CheckpointRecordsComparisonMenuTitle"
	{
		"#format"	"{1:s},{2:s},{3:s}"
		"pt"		"Comparação de registros de checkpoint\n{1} - {2} em {3}"
	}
	"CheckpointReachTime"
	{
		"pt"		"Tempo"
	}
	"CheckpointFinish"
	{
		"pt"		"Mapa terminado"
	}
	"StageFinishTime"
	{
		"#format"	"{1:d}"
		"pt"		"Tempo Final do Estágio {1}"
	}
	"StageAttempts"
	{
		"pt"		"Tentativas"
	}
	"NoCheckpointRecords"
	{
		"pt"		"Nenhum Registro de Checkpoint."
	}
	"WRDeleteRecord"
	{
		"pt"		"Excluir registro"
	}
	"WRPointsCap"
	{
		"pt"		"Pontos"
	}
	"WRRecord"
	{
		"pt"		"registros"
	}
	"WRRank"
	{
		"pt"		"Rank"
	}
	"WRRecordFor"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Registros para {1}: [{2}]"
	}
	"WRStrafes"
	{
		"pt"		"Strafes"
	}
	"WRCompletions"
	{
		"pt"		"Conclusões"
	}
	"WRStyle"
	{
		"pt"		"Estilo"
	}
	"WRStyleNothing"
	{
		"pt"		"Nada."
	}
	"WRTime"
	{
		"pt"		"Tempo"
	}
	// ---------- Messages ---------- //
	"CheckpointTime"
	{
		"#format"	"{1:s},{2:d},{3:s},{4:s},{5:d},{6:s},{7:s},{8:s},{9:s}"
		"pt"		"Tempo do Checkpoint ({1}{2}{3}/{4}{5}{6}): {7}{8}{9}."
	}
	"WRCheckpointTime"
	{
		"#format"	"{1:s},{2:d},{3:s},{4:s},{5:d},{6:s},{7:s},{8:s},{9:s},{10:s}"
		"pt"		"Tempo do Checkpoint ({1}{2}{3}/{4}{5}{6}): {7}{8}{9} (SR: {10})."
	}
	"WRPBCheckpointTime"
	{
		"#format"	"{1:s},{2:d},{3:s},{4:s},{5:d},{6:s},{7:s},{8:s},{9:s},{10:s},{11:s}"
		"pt"		"Tempo do Checkpoint ({1}{2}{3}/{4}{5}{6}): {7}{8}{9} (SR: {10} | PB: {11})."
	}
}
