#include <sourcemod>
#include <shavit>

#pragma newdecls required

public Plugin myinfo =
{
    name        = "S4F - Comandos de !ajuda",
    author      = "Proxychains",
    description = "",
    version     = "1.2",
    url         = "www.surfing4.fun"


}

Handle g_hURL;
char   g_szCachedURL[128];

public void OnPluginStart()
{
    g_hURL = CreateConVar("s4f_motd_url", "http://surfing4.fun/motd/surf/index.html", "MOTD url.");
    GetConVarString(g_hURL, g_szCachedURL, sizeof(g_szCachedURL));

    // RegConsoleCmd("sm_comandos", cmd_Comandos, "Server commands");
    // RegConsoleCmd("sm_commands", cmd_Comandos, "Server commands");
    // RegConsoleCmd("sm_help", cmd_Comandos, "Server commands");
    // RegConsoleCmd("sm_ajuda", cmd_Comandos, "Server commands");

    RegConsoleCmd("sm_help", cmd_motd, "Open servers help panel");
    RegConsoleCmd("sm_ajuda", cmd_motd, "Abre o painel de ajuda do servidor");
    RegConsoleCmd("sm_commands", cmd_motd, "Open servers help panel");
    RegConsoleCmd("sm_comandos", cmd_motd, "Abre o painel de ajuda do servidor");

    RegConsoleCmd("sm_discord", cmd_Discord, "Discord link");

    RegConsoleCmd("sm_donate", cmd_Donate, "Donate link");
    RegConsoleCmd("sm_vip", cmd_Donate, "Donate link");

    LoadTranslations("s4fhelp.phrases");
}

public Action cmd_motd(int client, int args)
{
    if (client > 0 && IsClientInGame(client))
    {
        ShowMOTDPanel(client, "Surfing4Fun", g_szCachedURL, MOTDPANEL_TYPE_URL);
    }
    return Plugin_Handled;
}

// public Action cmd_Comandos(int client, int args) {
//         if (IsClientConnected(client) && IsClientInGame(client))
//         {
//                 SetGlobalTransTarget(client);
//                 Shavit_PrintToChat(client, "%t", "Comandos");
//         }
// 		return Plugin_Handled;
// }
public Action cmd_Discord(int client, int args)
{
    if (IsClientConnected(client) && IsClientInGame(client))
    {
        SetGlobalTransTarget(client);
        Shavit_PrintToChat(client, "%t", "Discord");
    }
    return Plugin_Handled;
}

public Action cmd_Donate(int client, int args)
{
    if (IsClientConnected(client) && IsClientInGame(client))
    {
        SetGlobalTransTarget(client);
        Shavit_PrintToChat(client, "%t", "Donate");
    }
    return Plugin_Handled;
}