"Phrases"
{
	"PaintMenuTitle"
	{
		"pt"		"Menu de Pintura"
	}
	"PaintTips"
	{
		"pt"		"Dicas: Execute: bind <TECLA> \"+paint\" no console para vincular uma tecla para pintar \n  "
	}
	"ModeContinuous"
	{
		"pt"		"Contínuo"
	}
	"ModeSingle"
	{
		"pt"		"Único"
	}
	"TogglePaint"
	{
		"pt"		"Pintar"
	}
	"PaintMode"
	{
		"pt"		"Modo de pintura"
	}
	"ToggleErase"
	{
		"pt"		"Apagar"
	}
	"PaintColor"
	{
		"pt"		"Cor da Pintura"
	}
	"PaintSize"
	{
		"pt"		"Tamanho da Pintura"
	}
	"PaintObject"
	{
		"pt"		"Objeto da Pintura"
	}
	"PaintOptions"
	{
		"pt"		"Opções de Pintura"
	}
	"PaintOptionMenuTitle"
	{
		"pt"		"Opções	de Pintura"
	}
	"PaintEraser"
	{
		"pt"		"Modo deletar pintura"
	}
	"EraserOn"
	{
		"pt"		"LIGADO"
	}
	"EraserOff"
	{
		"pt"		"DESLIGADO"
	}
	"ObjectAll"
	{
		"pt"		"Todos os jogadores"
	}
	"ObjectSingle"
	{
		"pt"		"Próprio"
	}
	"PaintSelectPartner"
	{
		"pt"		"Selecione um parceiro"
	}
	"NoPartner"
	{
		"pt"		"Você não tem parceria com ninguém no momento."
	}
	"Refresh"
	{
		"pt"		"Atualizar"
	}
	"ReceivePartnerRequest"
	{
		"pt"		"Receber pedidos de parceria"
	}
	"RemovePartner"
	{
		"pt"		"Remover parceiro"
	}
	"SendPartnerRequest"
	{
		"pt"		"Enviar pedido para um jogador"
	}
	"NoPartnerFound"
	{
		"pt"		"Parceiro não encontrado"
	}
	"RequestSent"
	{
		"pt"		"Pedido enviado"
	}
	"ResponseWaitingMenuTitle"
	{
		"#format"	"{1:s}"
		"pt"		"Esperando resposta de {1}"
	}
	"ReceivedRequest"
	{
		"#format"	"{1:s}"
		"pt"		"Pedido de parceria recebido de {1}"
	}
	"AcceptRequest"
	{
		"pt"		"Aceitar"
	}
	"DeclineRequest"
	{
		"pt"		"Negar"
	}
	"CancelRequest"
	{
		"pt"		"Cancelar"
	}
	"MenuAutoClose"
	{
		"pt"		"Menu fechara em 10 segundos"
	}
	"MenuClose"
	{
		"pt"		"Fechar"
	}
	"RequestAccepted"
	{
		"#format"	"{1:s}"
		"pt"		"Seu pedido foi aceito por {1}"
	}
	"RequestDeclined"
	{
		"#format"	"{1:s}"
		"pt"		"Seu pedido foi negado por {1}"
	}
	"RequestCanceled"
	{
		"#format"	"{1:s}"
		"pt"		"Este pedido foi cancelado por {1}"
	}
	"RequestAborted"
	{
		"#format"	"{1:s}"
		"pt"		"Parceria com {1} abortada"
	}
	"Partnered"
	{
		"#format"	"{1:s},{2:s},{3:s}"
		"pt"		"Agora você é parceiro com {1}{2}{3}."
	}
	"Unpartnered"
	{
		"#format"	"{1:s},{2:s},{3:s}"
		"pt"		"Você {1}{2}{3}."
	}
	"PartnerDisconnected"
	{
		"#format"	"{1:s},{2:s},{3:s}"
		"pt"		"Seu parceiro {1}{2}{3} desconectou"
	}
	"PaintModeChangeError"
	{
		"pt"		"Você não pode mudar o modo enquanto está pintando"
	}
	"PaintClear"
	{
		"pt"		"Limpar Pintura"
	}
	"PaintCleared"
	{
		"pt"		"A pintura foi limpa."
	}
	"PaintColorMenuTitle"
	{
		"pt"		"Selecione Uma Cor: "
	}
	"PaintSizeMenuTitle"
	{
		"pt"		"Selecione Um Tamanho: "
	}
	"PaintSizeSmall"
	{
		"pt"		"Pequeno"
	}
	"PaintSizeMedium"
	{
		"pt"		"Médio"
	}
	"PaintSizeLarge"
	{
		"pt"		"Grande"
	}
	"PaintColorRandom"
	{
		"pt"		"Aleatório"
	}
	"PaintColorWhite"
	{
		"pt"		"Branco"
	}
	"PaintColorBlack"
	{
		"pt"		"Preto"
	}
	"PaintColorBlue"
	{
		"pt"		"Azul"
	}
	"PaintColorLightBlue"
	{
		"pt"		"Azul Claro"
	}
	"PaintColorBrown"
	{
		"pt"		"Marrom"
	}
	"PaintColorCyan"
	{
		"pt"		"Ciano"
	}
	"PaintColorGreen"
	{
		"pt"		"Verde"
	}
	"PaintColorDarkGreen"
	{
		"pt"		"Verde Escuro"
	}
	"PaintColorRed"
	{
		"pt"		"Vermelho"
	}
	"PaintColorOrange"
	{
		"pt"		"Laranja"
	}
	"PaintColorYellow"
	{
		"pt"		"Amarelo"
	}
	"PaintColorPink"
	{
		"pt"		"Rosa"
	}
	"PaintColorLightPink"
	{
		"pt"		"Rosa Claro"
	}
	"PaintColorPurple"
	{
		"pt"		"Roxo"
	}
}
