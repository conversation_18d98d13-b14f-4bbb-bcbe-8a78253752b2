#pragma semicolon 1
#pragma newdecls required

#include <sourcemod>
#include <sdktools>
#include <shavit>

ConVar g_cvEnableHoldTele;

bool   g_bIsFrozenTeleporting[MAXPLAYERS + 1];
float  g_fFrozenPosition[MAXPLAYERS + 1][3];      // Stores the XYZ position of the CP
float  g_fFrozenViewAngles[MAXPLAYERS + 1][3];    // 0: pitch, 1: yaw
int    g_iFrozenAtCPIndex[MAXPLAYERS + 1];        // Stores the index of the CP the player is frozen at
public Plugin myinfo =
{
    name        = "S4F - Hold-Key Teleport & Freeze",
    author      = "Proxychains & Gemini xdd",
    description = "Teleports to checkpoint on key press (+tele), freezes player, unfreezes on key release.",
    version     = "1.1",
    url         = "https://surfing4.fun"
};

public void OnPluginStart()
{
    g_cvEnableHoldTele = CreateConVar("sm_holdtele_enable", "1", "Enable the hold-to-teleport-and-freeze feature.", FCVAR_NOTIFY);

    RegConsoleCmd("+tele", Command_StartTeleFreeze, "Teleport to CP and freeze (on key press)");
    RegConsoleCmd("-tele", Command_EndTeleFreeze, "Unfreeze from CP teleport (on key release)");
    RegConsoleCmd("+sm_tele", Command_StartTeleFreeze, "Teleport to CP and freeze (on key press)");
    RegConsoleCmd("-sm_tele", Command_EndTeleFreeze, "Unfreeze from CP teleport (on key release)");

    HookEvent("player_death", Event_PlayerDeath, EventHookMode_Post);
    HookEvent("player_spawn", Event_PlayerSpawn, EventHookMode_Post);

    if (GetFeatureStatus(FeatureType_Native, "Shavit_GetCurrentCheckpoint") != FeatureStatus_Available || GetFeatureStatus(FeatureType_Native, "Shavit_GetTotalCheckpoints") != FeatureStatus_Available || GetFeatureStatus(FeatureType_Native, "Shavit_TeleportToCheckpoint") != FeatureStatus_Available)
    {
        SetFailState("Required Shavit checkpoint natives not found. Ensure 'shavit-checkpoints.smx' is loaded and running.");
    }
}

public void OnClientPutInServer(int client)
{
    g_bIsFrozenTeleporting[client] = false;
    g_fFrozenPosition[client][0]   = 0.0;
    g_fFrozenPosition[client][1]   = 0.0;
    g_fFrozenPosition[client][2]   = 0.0;
    g_iFrozenAtCPIndex[client]     = 0;
}

public void OnClientDisconnect(int client)
{
    g_bIsFrozenTeleporting[client] = false;    // Reset state on disconnect
    g_fFrozenPosition[client][0]   = 0.0;
    g_fFrozenPosition[client][1]   = 0.0;
    g_fFrozenPosition[client][2]   = 0.0;
    g_iFrozenAtCPIndex[client]     = 0;
}

public void Event_PlayerDeath(Event event, const char[] name, bool dontBroadcast)
{
    int client = GetClientOfUserId(event.GetInt("userid"));
    if (client > 0 && g_bIsFrozenTeleporting[client])
    {
        // Unfreeze just in case, though spawn should handle it c:
        UnfreezePlayer(client);
        g_bIsFrozenTeleporting[client] = false;
        g_fFrozenPosition[client][0]   = 0.0;
        g_fFrozenPosition[client][1]   = 0.0;
        g_fFrozenPosition[client][2]   = 0.0;
        g_iFrozenAtCPIndex[client]     = 0;
    }
}

public void Event_PlayerSpawn(Event event, const char[] name, bool dontBroadcast)
{
    int client = GetClientOfUserId(event.GetInt("userid"));
    if (client > 0 && g_bIsFrozenTeleporting[client])
    {
        // If they were somehow still marked as frozen on spawn, clear it.
        UnfreezePlayer(client);    // Ensure FL_FROZEN is cleared if it persisted
        g_bIsFrozenTeleporting[client] = false;
        g_fFrozenPosition[client][0]   = 0.0;
        g_fFrozenPosition[client][1]   = 0.0;
        g_fFrozenPosition[client][2]   = 0.0;
        g_iFrozenAtCPIndex[client]     = 0;
    }
    else if (client > 0)    // Always reset this on spawn
    {
        g_fFrozenPosition[client][0] = 0.0;
        g_fFrozenPosition[client][1] = 0.0;
        g_fFrozenPosition[client][2] = 0.0;
        g_iFrozenAtCPIndex[client]   = 0;
    }
}

public Action Command_StartTeleFreeze(int client, int args)
{
    if (!g_cvEnableHoldTele.BoolValue)
    {
        return Plugin_Handled;
    }

    if (!IsValidClient(client) || !IsPlayerAlive(client))
    {
        return Plugin_Handled;
    }

    if (g_bIsFrozenTeleporting[client])    // Already in this state
    {
        return Plugin_Handled;
    }

    int totalCPs = Shavit_GetTotalCheckpoints(client);
    if (totalCPs == 0)
    {
        Shavit_PrintToChat(client, "You have no checkpoints to teleport to.");
        return Plugin_Handled;
    }

    int cpIndex = Shavit_GetCurrentCheckpoint(client);
    // If no specific CP is selected (current is 0) and CPs exist, default to the first one.
    // Shavit's menu often defaults to CP 1 if current is 0.
    if (cpIndex == 0 && totalCPs > 0)
    {
        cpIndex = 1;
    }
    else if (cpIndex > totalCPs)    // Should not happen if Shavit_GetCurrentCheckpoint is reliable
    {
        cpIndex = totalCPs;    // Fallback to last CP
    }

    // Get the checkpoint data to store its view angles
    cp_cache_t cpData;
    if (!Shavit_GetCheckpoint(client, cpIndex, cpData, sizeof(cpData)))
    {
        // Shavit_PrintToChat(client, "Error: Could not retrieve checkpoint data for CP #%d.", cpIndex);
        return Plugin_Handled;
    }

    // Set the flag immediately so OnPlayerRunCmd can start working
    g_bIsFrozenTeleporting[client] = true;
    // Shavit_PrintToChat(client, "Debug: +sm_holdtele - State set to FROZEN.");

    // Store the checkpoint's view angles to lock the player's view to these
    // Also store the checkpoint's position
    g_fFrozenPosition[client][0]   = cpData.fPosition[0];
    g_fFrozenPosition[client][1]   = cpData.fPosition[1];
    g_fFrozenPosition[client][2]   = cpData.fPosition[2];

    g_fFrozenViewAngles[client][0] = cpData.fAngles[0];
    g_fFrozenViewAngles[client][1] = cpData.fAngles[1];
    g_fFrozenViewAngles[client][2] = cpData.fAngles[2];
    g_iFrozenAtCPIndex[client]     = cpIndex;

    // Teleport the player to the checkpoint. This sets their initial position.
    // The ownerClient is 'client' because they are teleporting to their own CPs.
    Shavit_TeleportToCheckpoint(client, cpIndex, true, client);
    RequestFrame(Frame_ApplyFreezeFlags, GetClientUserId(client));    // Schedule FL_FROZEN application for next frame
    Shavit_PauseTimer(client);

    return Plugin_Handled;
}

public Action Command_EndTeleFreeze(int client, int args)
{
    if (!g_cvEnableHoldTele.BoolValue)
    {
        return Plugin_Handled;
    }

    if (!IsValidClient(client))    // No need to check IsPlayerAlive, just unfreeze if they were marked
    {
        return Plugin_Handled;
    }

    // Shavit_PrintToChat(client, "Debug: -sm_holdtele - Attempting to UNFREEZE.");
    if (g_bIsFrozenTeleporting[client])
    {
        UnfreezePlayer(client);
        g_bIsFrozenTeleporting[client] = false;

        // Re-teleport to the checkpoint to apply its full state (including velocity)
        Shavit_TeleportToCheckpoint(client, g_iFrozenAtCPIndex[client], true, client);
        g_iFrozenAtCPIndex[client] = 0;    // Reset stored CP index
        Shavit_ResumeTimer(client);

        // Shavit_PrintToChat(client, "Debug: -sm_holdtele - State set to NOT FROZEN.");
    }

    return Plugin_Handled;
}

public void Frame_ApplyFreezeFlags(any userid)
{
    int client = GetClientOfUserId(userid);
    if (IsValidClient(client) && IsPlayerAlive(client) && g_bIsFrozenTeleporting[client])    // Check g_bIsFrozenTeleporting again
    {
        // Now, apply the FL_FROZEN flag. View angles were already stored.
        // g_bIsFrozenTeleporting was set in Command_StartTeleFreeze
        int flags = GetEntProp(client, Prop_Data, "m_fFlags");
        SetEntProp(client, Prop_Data, "m_fFlags", flags | FL_FROZEN);
        // Shavit_PrintToChat(client, "Debug: Frame_ApplyFreezeFlags - FL_FROZEN flag set.");
    }
    else if (IsValidClient(client)) {
        // Shavit_PrintToChat(client, "Debug: Frame_ApplyFreezeFlags - Conditions not met (Alive: %d, FrozenState: %d).", IsPlayerAlive(client), g_bIsFrozenTeleporting[client]);
    }
}

void UnfreezePlayer(int client)
{
    if (IsValidClient(client))
    {
        int flags = GetEntProp(client, Prop_Data, "m_fFlags");
        SetEntProp(client, Prop_Data, "m_fFlags", flags & ~FL_FROZEN);
        // Shavit_PrintToChat(client, "Debug: UnfreezePlayer - FL_FROZEN flag cleared.");
    }
}

public Action Shavit_OnUserCmdPre(int client, int &buttons, int &impulse, float vel[3], float angles[3], TimerStatus status, int track, int style, int mouse[2])
{
    // Check if the client is currently frozen by the plugin
    if (g_bIsFrozenTeleporting[client])
    {
        // Override the player's view angles with the stored frozen angles
        angles[0] = g_fFrozenViewAngles[client][0];    // Pitch
        angles[1] = g_fFrozenViewAngles[client][1];    // Yaw
        angles[2] = g_fFrozenViewAngles[client][2];    // Roll (usually 0)

        // Clear mouse input to prevent any slight view drift
        mouse[0]  = 0;
        mouse[1]  = 0;

        // Prevent player movement input while frozen
        buttons &= ~(IN_FORWARD | IN_BACK | IN_MOVELEFT | IN_MOVERIGHT | IN_JUMP | IN_DUCK);

        // Return Plugin_Changed to indicate that the angles/buttons were modified
        return Plugin_Changed;
    }

    return Plugin_Continue;
}