#include <sourcemod>
#include <shavit>
#include <float>       // For FloatAbs
#include <sdktools>    // For GetEntPropVector, SetEntPropVector, GetEntityFlags, GetEntityMoveType, GetVectorLength
#pragma semicolon 1
#pragma newdecls required

bool             gB_Opti[MAXPLAYERS + 1];
bool             gB_IntentionalStop[MAXPLAYERS + 1];    // To communicate from OnPlayerRunCmd to Shavit_OnProcessMovementPost
float            gF_PreviousSpeed3D[MAXPLAYERS + 1];    // Stores the 3D speed from the end of the previous tick
public Plugin myinfo =
{
    name        = "S4F - Speed Optimizer",
    author      = "KiD Fearless - s4f edits.",
    description = "Zero speed loss while on the style",
    version     = "1.1",
    url         = "http://surfing4.fun"
};

public void OnPluginStart()
{
    // Hook player spawn to reset speed on spawn
    HookEvent("player_spawn", Event_PlayerSpawn_Optimizer, EventHookMode_Post);
    // Note: Shavit_OnProcessMovementPost is a forward, no explicit hooking needed here, just define the function.
}

public void OnClientConnected(int client)
{
    gB_Opti[client]            = false;
    gF_PreviousSpeed3D[client] = 0.0;

    gB_IntentionalStop[client] = false;
}

public void OnClientDisconnect(int client)    // Good practice to reset on disconnect
{
    gB_Opti[client]            = false;
    gF_PreviousSpeed3D[client] = 0.0;
    gB_IntentionalStop[client] = false;
}

public void Shavit_OnStyleChanged(int client, int oldstyle, int newstyle)
{
    // This requires the style to have  style setting "s4f_speedoptimizer" "1" in the style's config.
    gB_Opti[client] = Shavit_GetStyleSettingBool(newstyle, "s4f_speedoptimizer");

    if (!gB_Opti[client])
    {
        gF_PreviousSpeed3D[client] = 0.0;    // Reset speed memory
    }
    else    // Optimizer is enabled for this style
    {
        // Get current speed to initialize gF_PreviousSpeed3D if they are already moving
        if (IsPlayerAlive(client))
        {
            float initialVel[3];
            GetEntPropVector(client, Prop_Data, "m_vecVelocity", initialVel);
            gF_PreviousSpeed3D[client] = GetVectorLength(initialVel, true);                    // Use actual 3D speed
            if (gF_PreviousSpeed3D[client] < 1.0) gF_PreviousSpeed3D[client] = 0.0;            // Avoid tiny initial values
            if (gF_PreviousSpeed3D[client] > 10000.0) gF_PreviousSpeed3D[client] = 10000.0;    // Cap
        }
        else {
            gF_PreviousSpeed3D[client] = 0.0;    // Reset speed memory
        }
    }
}

public void Shavit_OnRestart(int client, int track, bool tostartzone)    // Matched signature
{
    // On timer restart, reset the baseline speed to ensure a fresh start feel.
    // Player's actual velocity will be handled by the timer's restart logic (e.g., teleportation).
    gF_PreviousSpeed3D[client] = 0.0;
    gB_IntentionalStop[client] = false;    // Reset this state too
}

public void Event_PlayerSpawn_Optimizer(Event event, const char[] name, bool dontBroadcast)
{
    int client = GetClientOfUserId(event.GetInt("userid"));
    if (client > 0 && IsValidClient(client))    // Check IsValidClient
    {
        gF_PreviousSpeed3D[client] = 0.0;      // Reset speed memory on spawn
        gB_IntentionalStop[client] = false;    // Reset this state too
    }
}

public Action OnPlayerRunCmd(int client, int &buttons, int &impulse, float vel[3], float angles[3], int &weapon, int &subtype, int &cmdnum, int &tickcount, int &seed, int mouse[2])
{
    if (!gB_Opti[client] || !IsValidClient(client, true))    // IsValidClient(client, true) checks alive
    {
        gB_IntentionalStop[client] = false;    // Ensure it's reset if opti is off or player invalid
        return Plugin_Continue;
    }

    MoveType moveType = GetEntityMoveType(client);
    if (moveType == MOVETYPE_NOCLIP || moveType == MOVETYPE_LADDER)
    {
        gB_IntentionalStop[client] = false;    // Not an intentional stop in these modes for our purpose
        return Plugin_Continue;
    }

    // Determine if the player is intentionally trying to stop or slow down.
    // This flag will be used in Shavit_OnProcessMovementPost.
    gB_IntentionalStop[client] = false;    // Default to not stopping intentionally

    bool isOnGround            = (GetEntityFlags(client) & FL_ONGROUND) != 0;

    // Case 1: Player is on the ground and not actively trying to move or jump.
    // This implies they want to stop or are already stopped.
    if (isOnGround && !(buttons & IN_FORWARD) && !(buttons & IN_BACK) && !(buttons & IN_MOVELEFT) && !(buttons & IN_MOVERIGHT) && !(buttons & IN_JUMP) && FloatAbs(vel[0]) < 0.1 &&    // Not commanding forward/backward movement via script/tas
        FloatAbs(vel[1]) < 0.1)                                                                                                                                                        // Not commanding sideward movement via script/tas
    {
        gB_IntentionalStop[client] = true;
    }
    // Case 2: Player is actively commanding backward movement (pressing 'S' or equivalent).
    // vel[0] is forwardmove. Negative means backward.
    else if (vel[0] < -0.01)
    {
        gB_IntentionalStop[client] = true;
    }

    return Plugin_Continue;    // No direct velocity modification here.
}

public void Shavit_OnProcessMovementPost(int client)
{
    if (!gB_Opti[client] || !IsValidClient(client, true))    // IsValidClient(client, true) checks alive
    {
        return;
    }

    MoveType moveType = GetEntityMoveType(client);
    if (moveType == MOVETYPE_NOCLIP || moveType == MOVETYPE_LADDER)
    {
        // When on a ladder or noclip, speed is controlled differently.
        // Update previous speed to current ladder/noclip speed so it's correct when they get off.
        float currentNonOptiVel[3];
        GetEntPropVector(client, Prop_Data, "m_vecVelocity", currentNonOptiVel);
        gF_PreviousSpeed3D[client] = GetVectorLength(currentNonOptiVel, true);             // Use actual 3D speed
        if (gF_PreviousSpeed3D[client] > 10000.0) gF_PreviousSpeed3D[client] = 10000.0;    // Cap
        return;
    }

    float vCurrentVelocity[3];
    GetEntPropVector(client, Prop_Data, "m_vecVelocity", vCurrentVelocity);
    float fCurrentSpeed3D = GetVectorLength(vCurrentVelocity, true);    // Use actual 3D speed

    // If player is intentionally stopping (determined in OnPlayerRunCmd for this tick)
    if (gB_IntentionalStop[client])
    {
        gF_PreviousSpeed3D[client] = fCurrentSpeed3D;                                      // Allow natural deceleration/stop
        if (gF_PreviousSpeed3D[client] > 10000.0) gF_PreviousSpeed3D[client] = 10000.0;    // Cap
        return;
    }

    // Safety speed cap for gF_PreviousSpeed3D
    if (gF_PreviousSpeed3D[client] > 10000.0)
    {
        gF_PreviousSpeed3D[client] = 10000.0;
    }

    // Apply speed maintenance
    // If current 3D speed has dropped below the previously recorded 3D speed,
    // and the previous speed was significant.
    if (fCurrentSpeed3D < gF_PreviousSpeed3D[client] && gF_PreviousSpeed3D[client] > 10.0)
    {
        // Only apply if current speed is not effectively zero, to avoid division by zero.
        if (fCurrentSpeed3D > 0.01)    // Check against a small epsilon
        {
            float scale = gF_PreviousSpeed3D[client] / fCurrentSpeed3D;
            vCurrentVelocity[0] *= scale;
            vCurrentVelocity[1] *= scale;
            vCurrentVelocity[2] *= scale;    // Also scale vertical speed

            SetEntPropVector(client, Prop_Data, "m_vecVelocity", vCurrentVelocity);
            // After modification, update fCurrentSpeed3D to reflect the change for the next step
            // The new speed should be very close to gF_PreviousSpeed3D[client]
            fCurrentSpeed3D = gF_PreviousSpeed3D[client];    // Speed is now restored
        }
        else    // Current speed is ~0, but previous was high. Player likely hit a wall.
        {
            // Reset previous speed to prevent trying to accelerate from 0 to a high speed.
            fCurrentSpeed3D = 0.0;    // Speed is now effectively zero
        }
    }

    // Update gF_PreviousSpeed3D for the next tick.
    // This will be the current speed, whether it was maintained, increased naturally (e.g. ramp), or just set.
    gF_PreviousSpeed3D[client] = fCurrentSpeed3D;
    if (gF_PreviousSpeed3D[client] > 10000.0)
    {    // Final cap
        gF_PreviousSpeed3D[client] = 10000.0;
    }
}
