#include <shavit>
#include <clientprefs>
#define MaxPlayers 33

public Plugin myinfo =
{
    name        = "[ANY] FOV Manager",
    author      = "Tk /id/Teamkiller324",
    description = "Manage the viewmodel fov.",
    version     = "1.3",
    url         = "https://steamcommunity.com/id/Teamkiller324"


}

// Standalone module from Random Commands Plugin, originally called "Tk Unrestricted FOV"

int    g_FOV[MaxPlayers + 1] = { -1, ... };
char   Prefix[128];
ConVar fovEnable, fovMinimum, fovMaximum, fovPrefix;
Cookie fovCookie;

public void OnPluginStart()
{
    LoadTranslations("fov_manager.phrases");
    LoadTranslations("common.phrases");

    RegConsoleCmd("sm_fov", FovCmd, "FOV Manager - Set a custom fov on yourself");
    RegConsoleCmd("sm_randomfov", RandomFovCmd, "FOV Manager - Set a random fov on yourself");

    fovEnable  = CreateConVar("sm_fovmanager_enable", "1", "FOV Manager - Enable / Disable Unrestricted FOV", _, true, _, true, 1.0);
    fovMinimum = CreateConVar("sm_fovmanager_minimum", "10", "FOV Manager - Minimum Unrestricted FOV", _, true, 10.0, true, 360.0);
    fovMaximum = CreateConVar("sm_fovmanager_maximum", "180", "FOV Manager - Maximum Unrestricted FOV", _, true, 10.0, true, 360.0);
    fovPrefix  = CreateConVar("sm_fovmanager_prefix", "[Fov]", "FOV Manager - Chat prefix");
    fovPrefix.AddChangeHook(PrefixCallback);
    fovPrefix.GetString(Prefix, sizeof(Prefix));
    Format(Prefix, sizeof(Prefix), "%s", Prefix);

    fovCookie = new Cookie("sm_fovmanager_cookie", "Fov Manager", CookieAccess_Private);

    HookEvent("player_spawn", Player_Spawn);
}

void PrefixCallback(ConVar cvar, const char[] oldvalue, const char[] newvalue)
{
    cvar.GetString(Prefix, sizeof(Prefix));
    Format(Prefix, sizeof(Prefix), "%s", Prefix);
}

public void OnClientPostAdminCheck(int client)
{
    if (!IsAValidClient(client)) return;
    char cookie[8];
    fovCookie.Get(client, cookie, sizeof(cookie));
    if (strlen(cookie) > 0) g_FOV[client] = StringToInt(cookie);
}

public void OnClientDisconnect(int client)
{
    if (IsAValidClient(client))
    {
        if (g_FOV[client] > -1)
        {
            char cookie[8];
            IntToString(g_FOV[client], cookie, sizeof(cookie));
            fovCookie.Set(client, cookie);
        }
    }

    g_FOV[client] = -1;
}

Action FovCmd(int client, int args)
{
    if (!fovEnable.BoolValue) return Plugin_Handled;

    if (client == 0)
    {
        ReplyToCommand(client, "[FOV Manager] This command may only be used ingame");
        return Plugin_Handled;
    }

    int fov = GetCmdInt(1);

    if (args < 1 && g_FOV[client] > fovMinimum.IntValue)
    {
        Shavit_PrintToChat(client, "%s %t", Prefix, "#FOV_Disabled");
        SetFOV(client, 90);
        g_FOV[client] = -1;
        char buffer[16];
        IntToString(0, buffer, sizeof(buffer));
        fovCookie.Set(client, buffer);
        return Plugin_Handled;
    }
    else if (args < 1) {
        Shavit_PrintToChat(client, "%s %t", Prefix, "#FOV_Usage", fovMinimum.IntValue, fovMaximum.IntValue);
        return Plugin_Handled;
    }

    if (fov < fovMinimum.IntValue)
    {
        Shavit_PrintToChat(client, "%s %t", Prefix, "#FOV_Error_Minimum", fovMinimum.IntValue);
        return Plugin_Handled;
    }
    else if (fov > fovMaximum.IntValue) {
        Shavit_PrintToChat(client, "%s %t", Prefix, "#FOV_Error_Maximum", fovMaximum.IntValue);
        return Plugin_Handled;
    }

    SetFOV(client, fov);
    g_FOV[client] = fov;

    char val[16];
    IntToString(fov, val, sizeof(val));
    fovCookie.Set(client, val);
    Shavit_PrintToChat(client, "%s %t", Prefix, "#FOV_Set", fov);

    return Plugin_Handled;
}

Action RandomFovCmd(int client, int args)
{
    if (!fovEnable.BoolValue) return Plugin_Handled;

    if (client == 0)
    {
        ReplyToCommand(client, "[FOV Manager] This command may only be used ingame");
        return Plugin_Handled;
    }

    int picker = GetRandomInt(fovMinimum.IntValue, fovMaximum.IntValue);
    SetFOV(client, picker);
    g_FOV[client] = picker;

    Shavit_PrintToChat(client, "%s %t", Prefix, "#FOV_Randomized", picker);
    return Plugin_Handled;
}

void Player_Spawn(Event event, const char[] event_name, bool dontBroadcast)
{
    int userid = event.GetInt("userid");
    if (userid < 1) return;
    CreateTimer(0.1, Timer_Spawn, userid);
}

Action Timer_Spawn(Handle timer, int userid)
{
    int client = GetClientOfUserId(userid);
    if (IsAValidClient(client))
        if (g_FOV[client] > -1) SetFOV(client, g_FOV[client]);
    return Plugin_Handled;
}

bool IsAValidClient(int client)
{
    if (client < 1 || client > MaxPlayers) return false;
    if (!IsClientConnected(client)) return false;
    if (IsClientReplay(client)) return false;
    if (IsClientSourceTV(client)) return false;
    if (IsFakeClient(client)) return false;
    return true;
}

int GetCmdInt(int argnum)
{
    char dummy[16];
    GetCmdArg(argnum, dummy, sizeof(dummy));
    return StringToInt(dummy);
}

void SetFOV(int client, int value)
{
    SetEntProp(client, Prop_Send, "m_iFOV", value);
    SetEntProp(client, Prop_Send, "m_iDefaultFOV", value);
}