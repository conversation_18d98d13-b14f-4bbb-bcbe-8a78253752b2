#include <sourcemod>
#include <SteamWorks>
#include <sdktools>
#include <steamworks-profileurl>
#include <shavit>
#include <shavit/steamid-stocks>

#pragma newdecls required
#pragma semicolon 1

char     g_sMapName[PLATFORM_MAX_PATH];
int      g_iMapTier;

int      g_iMainColor;
int      g_iBonusColor;
int      gI_RestartCounter[MAXPLAYERS + 1][TRACKS_SIZE];

Handle   g_hWRTrie;

int      gI_Driver = Driver_unknown;
Database gH_SQL    = null;

ConVar   g_cvHostname;
ConVar   g_cvHostPort;
ConVar   g_cvWebhookRecords;
ConVar   g_cvWebhookMapChange;
ConVar   g_cvBotProfilePicture;
ConVar   g_cvMinimumrecords;
ConVar   g_cvBotUsername;
ConVar   g_cvFooterUrl;
ConVar   g_cvMainEmbedColor;
ConVar   g_cvBonusEmbedColor;
ConVar   g_cvSendBonusRecords;
ConVar   g_cvSendOffstyleRecords;
ConVar   g_cvSendStageRecords;
ConVar   g_cvMinAttemptTime;
ConVar   g_cvMapImageRootUrl;

public Plugin myinfo =
{
    name        = "[shavit] Discord WR Bot (Steamworks)",
    author      = "SlidyBat, improved by Sarrus / nimmy / Surfing4Fun",
    description = "Sends a Discord message when a new World Record is set.",
    version     = "2.6",
    url         = "https://github.com/Nimmy2222/shavit-discord"
};

public void OnPluginStart()
{
    g_cvMinimumrecords      = CreateConVar("shavit-discord-min-record", "0", "Minimum number of records before they are sent to the discord channel.", _, true, 0.0);
    g_cvWebhookRecords      = CreateConVar("shavit-discord-webhook-records", "", "The webhook to the discord channel where you want record messages to be sent.", FCVAR_PROTECTED);
    g_cvWebhookMapChange    = CreateConVar("shavit-discord-webhook-server-status", "", "The webhook to the discord channel where you want server status messages to be sent.", FCVAR_PROTECTED);
    g_cvBotProfilePicture   = CreateConVar("shavit-discord-profilepic", "https://surfing4.fun/assets/img/logo.webp", "link to pfp for the bot");
    g_cvFooterUrl           = CreateConVar("shavit-discord-footer-url", "https://surfing4.fun/assets/img/logo.webp", "The url of the footer icon, leave blank to disable.");
    g_cvBotUsername         = CreateConVar("shavit-discord-username", "S4F - World Records", "Username of the bot");
    g_cvMainEmbedColor      = CreateConVar("shavit-discord-main-color", "30, 255, 140", "Color of embed for when main wr is beaten");
    g_cvBonusEmbedColor     = CreateConVar("shavit-discord-bonus-color", "200, 55, 240", "Color of embed for when bonus wr is beaten");
    g_cvSendBonusRecords    = CreateConVar("shavit-discord-send-bonus", "1", "Whether to send bonus records or not 1 Enabled 0 Disabled");
    g_cvSendOffstyleRecords = CreateConVar("shavit-discord-send-offstyle", "1", "Whether to send offstyle records or not 1 Enabled 0 Disabled");
    g_cvSendStageRecords    = CreateConVar("shavit-discord-send-stage", "0", "Wheter to send a stage record or not 1 Enabled 0 Disabled");
    g_cvMinAttemptTime      = CreateConVar("shavit-discord-min-attempt-time", "1.0", "Minimum time a player must have on their timer for a stop/fail to count as an attempt.", _, true, 0.1);
    g_cvMapImageRootUrl     = CreateConVar("shavit-discord-map-img-root-url", "https://raw.githubusercontent.com/GimoDDak/SurfMapPics/refs/heads/Maps-and-bonuses/csgo/", "Root URL for map images. %s will be replaced with mapname.jpg or mapname_bX.jpg.");
    g_cvHostname            = FindConVar("hostname");
    g_cvHostPort            = FindConVar("hostport");

    HookConVarChange(g_cvMainEmbedColor, CvarChanged);
    HookConVarChange(g_cvBonusEmbedColor, CvarChanged);

    UpdateColorCvars();
    RegAdminCmd("sm_discordtest", CommandDiscordTest, ADMFLAG_ROOT);
    AutoExecConfig(true, "plugin.shavit-discord-s4f");
}

public void OnClientPutInServer(int client)
{
    // Initialize restart counters for all tracks for the client
    for (int i = 0; i < TRACKS_SIZE; i++)
    {
        gI_RestartCounter[client][i] = 0;
    }
}

public void UpdateColorCvars()
{
    char sMainColor[32];
    char sBonusColor[32];
    g_cvMainEmbedColor.GetString(sMainColor, sizeof(sMainColor));
    g_cvBonusEmbedColor.GetString(sBonusColor, sizeof(sBonusColor));
    g_iMainColor  = RGBStrToShiftedInt(sMainColor);
    g_iBonusColor = RGBStrToShiftedInt(sBonusColor);
}

int RGBStrToShiftedInt(char fullStr[32])
{
    char rgbStrs[3][5];
    int  strs = ExplodeString(fullStr, ",", rgbStrs, sizeof(rgbStrs), sizeof(rgbStrs[]));
    if (strs < 3)
    {
        return 255 << (2 * 8);
    }
    int adjustedInt = 0;
    for (int i = 0; i < 3; i++)
    {
        int color   = StringToInt(rgbStrs[i]);
        adjustedInt = (adjustedInt & ~(255 << ((2 - i) * 8))) | ((color & 255) << ((2 - i) * 8));
    }
    return adjustedInt;
}

public void CvarChanged(ConVar convar, const char[] oldValue, const char[] newValue)
{
    UpdateColorCvars();
}

public void OnMapStart()
{
    GetCurrentMap(g_sMapName, sizeof(g_sMapName));
    StringMap tiersMap = null;
    int       tier_val = 0;
    tiersMap           = Shavit_GetMapTiers();
    tiersMap.GetValue(g_sMapName, tier_val);
    g_iMapTier = tier_val;    // Store as int

    // Initialize the WR Trie for the current map
    g_hWRTrie  = CreateTrie();

    gH_SQL     = Shavit_GetDatabase(gI_Driver);
    char sQuery[512];
    FormatEx(sQuery, sizeof(sQuery), "SELECT wrs.style, wrs.track, wrs.auth, users.name FROM wrs LEFT JOIN users ON wrs.auth = users.auth WHERE wrs.map = '%s';", g_sMapName);
    QueryLog(gH_SQL, SQL_GetActualWRList_Callback, sQuery, 0, DBPrio_High);

    // [TODO] Refactor
    char webhook[256];
    g_cvWebhookMapChange.GetString(webhook, sizeof(webhook));
    if (webhook[0] == '\0')
    {
        // LogError("Discord webhook for server status is not set. Map change message will not be sent.");
    }

    char botUserName[128];
    g_cvBotUsername.GetString(botUserName, sizeof(botUserName));

    char botAvatar[1024];
    g_cvBotProfilePicture.GetString(botAvatar, sizeof(botAvatar));

    char recordTxt[128];    // Increased size
    Format(recordTxt, sizeof(recordTxt), "Server is now playing: %s (Tier %d)", g_sMapName, g_iMapTier);

    // Construct the final JSON string in one Format() call.
    char jsonStr[4096];
    Format(jsonStr, sizeof(jsonStr),
           "{\"username\":\"%s\",\"avatar_url\":\"%s\",\"embeds\":[{\"title\":\"%s\"}]}",
           botUserName,
           botAvatar,
           recordTxt);
    // PrintToServer("[shavit-discord] OnMapStart: Map change message JSON (currently not sent): %s", jsonStr);
    // SendMessageRaw(jsonStr, webhook);
}

public void SQL_GetActualWRList_Callback(Database db, DBResultSet results, const char[] error, any data)
{
    if (results == null)
    {
        LogError("SQL query failed: %s", error);
        return;
    }

    while (results.FetchRow())
    {
        int  style   = results.FetchInt(0);
        int  track   = results.FetchInt(1);

        int  steamID = results.FetchInt(2);

        char sName[128];
        results.FetchString(3, sName, sizeof(sName));

        char steam64[40];
        AccountIDToSteamID64(steamID, steam64, sizeof(steam64));

        char key[64];
        Format(key, sizeof(key), "%d_%d", style, track);

        // Combine auth and sName into one string (using a '|' as a delimiter)
        // TODO: Use explode string
        char combinedValue[128];
        Format(combinedValue, sizeof(combinedValue), "%s|%s", steam64, sName);

        // DEBUG
        // PrintToServer("%i|%s", steamID, steam64);
        SetTrieString(g_hWRTrie, key, combinedValue);
    }
}

// Fetches old WR holder details, updates trie with new WR holder, and returns if an old holder was found.
// Parameters:
//   key: The style_track key for the trie.
//   oldHolderAuthOut: Buffer to store the old holder's auth ID.
//   oldHolderNameOut: Buffer to store the old holder's name.
//   newHolderName: Name of the new WR holder.
//   newHolderAuthId: Auth ID of the new WR holder.
// Returns:
//   true if a previous WR holder was found and their details were extracted.
//   false if no previous holder was found or data was malformed.
bool GetOldHolderAndUpdateTrie(const char[] key,
                               char[] oldHolderAuthOut, int oldHolderAuthOutLen,
                               char[] oldHolderNameOut, int oldHolderNameOutLen,
                               const char[] newHolderName, const char[] newHolderAuthId)
{
    bool bFoundOldHolder = false;
    char combinedOldValue[128];

    // 1. Try to get the old holder from the trie.
    if (GetTrieString(g_hWRTrie, key, combinedOldValue, sizeof(combinedOldValue)))
    {
        int pos = StrContains(combinedOldValue, "|", false);
        if (pos != -1)
        {
            // Extract old auth ID
            int iterate = 0;
            for (iterate = 0; iterate < pos && iterate < oldHolderAuthOutLen - 1; iterate++)
            {
                oldHolderAuthOut[iterate] = combinedOldValue[iterate];
            }
            oldHolderAuthOut[iterate] = '\0';

            // Extract old name
            int nameIndex             = 0;
            for (int i = pos + 1; combinedOldValue[i] != '\0' && nameIndex < oldHolderNameOutLen - 1; i++, nameIndex++)
            {
                oldHolderNameOut[nameIndex] = combinedOldValue[i];
            }
            oldHolderNameOut[nameIndex] = '\0';

            bFoundOldHolder             = true;
        }
        else
        {
            PrintToServer("[shavit-discord] Error: Old trie value for key '%s' is malformed: '%s'", key, combinedOldValue);
            oldHolderAuthOut[0] = '\0';
            oldHolderNameOut[0] = '\0';
        }
    }
    else
    {
        // No previous holder found
        // PrintToServer("[shavit-discord] No previous trie entry for key '%s'", key);
        oldHolderAuthOut[0] = '\0';
        oldHolderNameOut[0] = '\0';
    }

    // 2. Update the trie with the new holder's info.
    char combinedNewValue[128];
    Format(combinedNewValue, sizeof(combinedNewValue), "%s|%s", newHolderAuthId, newHolderName);
    SetTrieString(g_hWRTrie, key, combinedNewValue);

    return bFoundOldHolder;
}

public Action CommandDiscordTest(int client, int args)
{
    int track = GetCmdArgInt(1);
    int style = GetCmdArgInt(2);    // Default to 0 if not provided or invalid
    Shavit_OnWorldRecord(client, style, 12.3, 35, 23, 93.25, track, 0, 17.01, 18.5, 100.0, 500.0, 800.0, GetTime());

    PrintToChat(client, "[shavit-discord] Discord Test Message has been sent.");
    return Plugin_Handled;
}

// Listen
public void Shavit_OnWorldRecord(int client, int style, float time, int jumps, int strafes, float sync, int track, int stage, float oldwr, float oldtime, float perfs, float avgvel, float maxvel, int timestamp)
{
    if (g_cvMinimumrecords.IntValue > 0 && Shavit_GetRecordAmount(style, track) < g_cvMinimumrecords.IntValue)
    {
        PrintToServer("[shavit-discord] Record not sent: Less than minimum records (%d < %d)", Shavit_GetRecordAmount(style, track), g_cvMinimumrecords.IntValue);
        return;
    }
    if (!(g_cvSendOffstyleRecords.IntValue) && style != 0)
    {
        PrintToServer("[shavit-discord] Record not sent: Offstyle records disabled and style is %d", style);
        return;
    }
    if (!(g_cvSendBonusRecords.IntValue) && track != Track_Main)
    {
        PrintToServer("[shavit-discord] Record not sent: Bonus records disabled and track is %d", track);
        return;
    }

    // stage == 0 means full run (main or bonus)
    // stage > 0 means individual stage run (on main track, or potentially a bonus if it has stages)
    if (stage > 0)    // It's an individual stage record
    {
        if (!g_cvSendStageRecords.BoolValue)
        {
            PrintToServer("[shavit-discord] Record not sent: Stage records disabled and stage is %d", stage);
            return;
        }
    }
    // If stage == 0, it's a full run (main or bonus).
    // These are generally always sent, respecting the other cvars like send-bonus etc.
    // The specific case of a "full main run" (stage == g_iMainTrackStageCount from the old filter)
    // is now covered by shavit-wr providing stage == 0 for full main runs.

    FormatEmbedMessage(client, style, time, jumps, strafes, sync, track, stage, oldwr);
}

// Manually construct JSON string to avoid heap allocations from JSON objects.
void FormatEmbedMessage(int client, int style, float time, int jumps, int strafes, float sync, int track, int stage, float oldwr)
{
    char styleMsg[512];
    Shavit_GetStyleStrings(style, sStyleName, styleMsg, sizeof(styleMsg));

    char  recordTxt[1024];
    float points_gained = 0.0;
    int   current_rank;
    int   record_count;

    // stage == 0 from Shavit_OnWorldRecord means a full run (main or bonus)
    // stage > 0 means an individual stage completion WR
    if (stage > 0)    // Individual Stage WR
    {
        current_rank  = Shavit_GetStageRankForTime(style, time, stage);    // Assuming stage WRs are on Track_Main implicitly by Shavit's design
        record_count  = Shavit_GetStageRecordAmount(style, stage);
        points_gained = Shavit_GuessPointsForTime(Track_Main, stage, style, current_rank, g_iMapTier, record_count);
        Format(recordTxt, sizeof(recordTxt), "Tier %d · Main Track - Stage %d · Style: %s · Points: %.2f", g_iMapTier, stage, styleMsg, points_gained);
    }
    else    // Full Run (Main or Bonus)
    {
        current_rank  = Shavit_GetRankForTime(style, time, track);
        record_count  = Shavit_GetRecordAmount(style, track);
        points_gained = Shavit_GuessPointsForTime(track, 0, style, current_rank, g_iMapTier, record_count);    // stage 0 for full runs

        if (track == Track_Main)
        {
            Format(recordTxt, sizeof(recordTxt), "Tier %d · Track: Main · Style: %s · Points: %.2f", g_iMapTier, styleMsg, points_gained);
        }
        else    // Bonus Track
        {
            Format(recordTxt, sizeof(recordTxt), "Tier %d · Track: Bonus %i · Style: %s · Points: %.2f", g_iMapTier, track, styleMsg, points_gained);
        }
    }

    char authId[64];
    GetClientAuthId(client, AuthId_SteamID64, authId, sizeof(authId));

    char name[MAX_NAME_LENGTH];
    GetClientName(client, name, sizeof(name));
    SanitizeName(name);

    char playerUrl[512];
    Format(playerUrl, sizeof(playerUrl), "http://www.steamcommunity.com/profiles/%s", authId);

    char playerProfilePicture[1024];
    if (!Sw_GetProfileUrl(client, playerProfilePicture, sizeof(playerProfilePicture)))
    {
        PrintToConsole(client, "Shavit-Discord: Failed to find profile picture URL");
        g_cvBotProfilePicture.GetString(playerProfilePicture, sizeof(playerProfilePicture));
    }

    char styleTrack[64];
    Format(styleTrack, sizeof(styleTrack), "%d_%d", style, track);

    // Me mama Proxychains
    // naah me mama você .sh :)
    char oldWRPlayerAuth[64];
    char oldWRPlayerName[64];
    bool bHadPreviousHolder = GetOldHolderAndUpdateTrie(styleTrack, oldWRPlayerAuth, sizeof(oldWRPlayerAuth), oldWRPlayerName, sizeof(oldWRPlayerName), name, authId);

    char oldWRPlayerUrl[512];
    char oldWrTimeStr[32];
    char previousTimeFieldValue[256];

    if (bHadPreviousHolder && oldwr > 0.0)
    {
        SanitizeName(oldWRPlayerName);
        Format(oldWRPlayerUrl, sizeof(oldWRPlayerUrl), "http://www.steamcommunity.com/profiles/%s", oldWRPlayerAuth);
        FormatSeconds(oldwr, oldWrTimeStr, sizeof(oldWrTimeStr));
        Format(oldWrTimeStr, sizeof(oldWrTimeStr), "%ss", oldWrTimeStr);
        Format(previousTimeFieldValue, sizeof(previousTimeFieldValue), "%s\\n[%s](%s)", oldWrTimeStr, oldWRPlayerName, oldWRPlayerUrl);
    }
    else {
        Format(previousTimeFieldValue, sizeof(previousTimeFieldValue), "N/A");
    }

    char newTimeStr[32];
    FormatSeconds(time, newTimeStr, sizeof(newTimeStr));
    Format(newTimeStr, sizeof(newTimeStr), "%ss", newTimeStr);

    float diff = time - oldwr;
    // If oldwr is 0.0 (no previous record), diff will be equal to 'time'.
    char  diffWrTimeStr[32];

    // Pass the absolute difference to FormatSeconds so it doesn't add its own sign.
    FormatSeconds(FloatAbs(diff), diffWrTimeStr, sizeof(diffWrTimeStr));
    // Prepend "-" if diff is negative (better time), or "+" if diff is positive (new time if no old WR. will set this emty for now, looks better?).
    Format(diffWrTimeStr, sizeof(diffWrTimeStr), "%s%ss", (diff < 0.0 ? "-" : ""), diffWrTimeStr);

    char statsFieldValue[128];
    Format(statsFieldValue, sizeof(statsFieldValue), "Strafes: %i · Sync: %.2f%% · Jumps: %i · Attempts: %i", strafes, sync, jumps, gI_RestartCounter[client][track]);

    char hostname[512];
    g_cvHostname.GetString(hostname, sizeof(hostname));

    char hostPort[512];
    g_cvHostPort.GetString(hostPort, sizeof(hostPort));

    char footerUrl[1024];
    g_cvFooterUrl.GetString(footerUrl, sizeof(footerUrl));

    char mapImageRootUrlStr[512];
    g_cvMapImageRootUrl.GetString(mapImageRootUrlStr, sizeof(mapImageRootUrlStr));

    char mapImageUrl[1024];
    if (track == Track_Main)
    {
        Format(mapImageUrl, sizeof(mapImageUrl), "%s%s.jpg", mapImageRootUrlStr, g_sMapName);
    }
    else
    {
        Format(mapImageUrl, sizeof(mapImageUrl), "%s%s_b%i.jpg", mapImageRootUrlStr, g_sMapName, track);
    }

    // Use the integer value for color directly in the JSON
    int  embed_color_int = (track == Track_Main && style == 0) ? g_iMainColor : g_iBonusColor;

    char botUserName[128];
    g_cvBotUsername.GetString(botUserName, sizeof(botUserName));

    char botAvatar[1024];
    g_cvBotProfilePicture.GetString(botAvatar, sizeof(botAvatar));

    char siteMapUrl[1024];
    Format(siteMapUrl, sizeof(siteMapUrl), "https://www.surfing4.fun/index.php?sv=surf&m=%s&s=%i&t=%i", g_sMapName, style, track);

    // Construct the final JSON string in one Format() call.
    char jsonStr[4096];
    Format(jsonStr, sizeof(jsonStr), "{\"username\":\"%s\",\"avatar_url\":\"%s\",\"embeds\":[{\"description\":\"%s\",\"color\":%d,\"author\":{\"name\":\"New server record on %s!\",\"url\":\"%s\",\"icon_url\":\"%s\"},\"fields\":[{\"name\":\"Player\",\"value\":\"[%s](%s)\",\"inline\":true},{\"name\":\"Time\",\"value\":\"%s\\n*(%s)*\",\"inline\":true},{\"name\":\"Previous time\",\"value\":\"%s\",\"inline\":true},{\"name\":\"Run stats\",\"value\":\"%s\"}],\"image\":{\"url\":\"%s\"},\"footer\":{\"text\":\"%s  ·  servers.surfing4.fun:%s\",\"icon_url\":\"%s\"}}]}",
           botUserName,
           botAvatar,
           recordTxt,
           embed_color_int,
           g_sMapName,
           siteMapUrl,
           playerProfilePicture,
           name,
           playerUrl,
           newTimeStr,
           diffWrTimeStr,
           previousTimeFieldValue,
           statsFieldValue,
           mapImageUrl,
           hostname,
           hostPort,
           footerUrl);

    // [TODO] Refactor
    char webhook[256];
    g_cvWebhookRecords.GetString(webhook, sizeof(webhook));
    if (webhook[0] == '\0')
    {
        LogError("Discord webhook is not set.");
        return;
    }

    SendMessageRaw(jsonStr, webhook);
}

void SendMessageRaw(const char[] jsonStr, const char[] webhook)
{
    Handle request = SteamWorks_CreateHTTPRequest(k_EHTTPMethodPOST, webhook);
    SteamWorks_SetHTTPRequestRawPostBody(request, "application/json", jsonStr, strlen(jsonStr));
    SteamWorks_SetHTTPCallbacks(request, OnMessageSent);
    SteamWorks_SendHTTPRequest(request);
}

public void OnMessageSent(Handle request, bool failure, bool requestSuccessful, EHTTPStatusCode statusCode, DataPack pack)
{
    if (failure || !requestSuccessful || statusCode != k_EHTTPStatusCode204NoContent)
    {
        LogError("Failed to send message to Discord. Response status: %d.", statusCode);
    }
    delete request;
}

void SanitizeName(char[] name)
{
    ReplaceString(name, MAX_NAME_LENGTH, "(", "", false);
    ReplaceString(name, MAX_NAME_LENGTH, ")", "", false);
    ReplaceString(name, MAX_NAME_LENGTH, "]", "", false);
    ReplaceString(name, MAX_NAME_LENGTH, "[", "", false);
}

// Attempts counter logic - we need to store the counter for each client/track
public bool Shavit_OnStopPre(int client, int track)
{
    if (!IsValidClient(client)) return true;    // Allow stop

    float       currentTime           = Shavit_GetClientTime(client);
    TimerStatus status                = Shavit_GetTimerStatus(client);
    float       minAttemptTime        = g_cvMinAttemptTime.FloatValue;
    int         currentTrackForClient = Shavit_GetClientTrack(client);

    // Shavit_PrintToChat(client, "[Attempts] OnStopPre: TrkPrm %d, CliTrk %d, Stat %d, CurT %.2f, MinT %.1f, CntBef %d",
    //     track, currentTrackForClient, view_as<int>(status), currentTime, minAttemptTime, gI_RestartCounter[client][track]);

    if (status == Timer_Running && currentTrackForClient == track && currentTime > minAttemptTime && track >= 0 && track < TRACKS_SIZE)
    {
        gI_RestartCounter[client][track]++;
        // Shavit_PrintToChat(client, "[Attempts] OnStopPre: Incremented counter for track %d to %d",
        //     track, gI_RestartCounter[client][track]);
    }
    else
    {
        // Shavit_PrintToChat(client, "[Attempts] OnStopPre: NOT MET. StatOK:%d, TrkMatch:%d, TimeOK:%d, TrkBoundsOK:%d",
        //     status == Timer_Running, currentTrackForClient == track, currentTime > minAttemptTime, track >= 0 && track < TRACKS_SIZE);
    }
    return true;
}

public void Shavit_OnFinish(int client, int style, float time, int jumps, int strafes, float sync, int track, float oldtime, float perfs, float avgvel, float maxvel, float startvel, float endvel, int timestamp)
{
    if (!IsValidClient(client)) return;

    // Check if this successfully finished run was significant enough to count as an attempt
    if (time > g_cvMinAttemptTime.FloatValue && track >= 0 && track < TRACKS_SIZE)
    {
        gI_RestartCounter[client][track]++;
        // Shavit_PrintToChat(client, "[Attempts] OnFinish: Incremented counter for track %d to %d (time: %.2f)",
        //     track, gI_RestartCounter[client][track], time);
    }
}

public Action Shavit_OnStartPre(int client, int track)
{
    if (!IsValidClient(client)) return Plugin_Continue;

    TimerStatus status      = Shavit_GetTimerStatus(client);
    float       currentTime = Shavit_GetClientTime(client);
    int         oldTrack    = Shavit_GetClientTrack(client);    // The track they *were* on before this new one starts

    // If timer was running (before being stopped by entering start zone) and the run was significant,
    // count it as an attempt on the *old* track.
    // This catches cases where a player walks into a start zone or is teleported into one by !back or trigger_teleport
    // without an explicit Shavit_OnStopPre trigger for that run.
    if (status == Timer_Running && currentTime > g_cvMinAttemptTime.FloatValue && oldTrack >= 0 && oldTrack < TRACKS_SIZE)
    {
        gI_RestartCounter[client][oldTrack]++;
        // Shavit_PrintToChat(client, "[Attempts] OnStartPre: Incremented counter for old track %d to %d (new track %d, old time %.2f)",
        //     oldTrack, gI_RestartCounter[client][oldTrack], track, currentTime);
    }
    return Plugin_Continue;
}