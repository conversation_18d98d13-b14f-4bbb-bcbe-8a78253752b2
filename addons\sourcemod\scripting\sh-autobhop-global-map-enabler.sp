#include <sourcemod>
#include <sdktools>

#pragma semicolon 1
#pragma newdecls required

Database         g_db = null;
ConVar           g_cvAutoBhop;
ConVar           g_cvEnableBhop;
char             g_sMapName[PLATFORM_MAX_PATH];

public Plugin myinfo =
{
    name        = "S4F - AutoBhop Enabler",
    author      = ".sh",
    description = "Sets sv_autobunnyhopping based on maptiers.autobhop_enabled",
    version     = "1.3",
    url         = "https://surfing4.fun"
};

public void OnPluginStart()
{
    g_cvAutoBhop   = FindConVar("sv_autobunnyhopping");
    g_cvEnableBhop = FindConVar("sv_enablebunnyhopping");

    char error[256];
    g_db = SQL_Connect("default", true, error, sizeof(error));

    if (g_db == null)
    {
        SetFailState("[autobhop] Failed to connect to database: %s", error);
    }

    RegAdminCmd("sm_setautobhop", Command_SetAutoBhop, ADMFLAG_CONVARS, "Usage: sm_setautobhop [map_name] - Toggles autobhop setting");
}

public void OnDatabaseConnected(Database db, const char[] error, any data)
{
    if (db == null)
    {
        SetFailState("[autobhop_enabler] DB connect failed: %s", error);
        return;
    }

    g_db = db;
    PrintToServer("[autobhop_enabler] DB connected.");
}

public void OnMapStart()
{
    if (g_db == null)
    {
        PrintToServer("[autobhop_enabler] DB not ready.");
        return;
    }

    GetCurrentMap(g_sMapName, sizeof(g_sMapName));

    char query[256];
    Format(query, sizeof(query), "SELECT autobhop_enabled FROM maptiers WHERE map = '%s' LIMIT 1;", g_sMapName);
    g_db.Query(OnBhopQueryResult, query);
}

public void OnBhopQueryResult(Database db, DBResultSet results, const char[] error, any data)
{
    if (results == null)
    {
        LogError("[autobhop_enabler] Query failed: %s", error);
        return;
    }

    if (!results.FetchRow())
    {
        PrintToServer("[autobhop_enabler] No autobhop setting found for map: %s", g_sMapName);
        return;
    }

    int enabled = results.FetchInt(0);
    g_cvAutoBhop.SetBool(enabled != 0);
    g_cvEnableBhop.SetBool(enabled != 0);

    PrintToServer("[autobhop_enabler] sv_autobunnyhopping set to %d for map: %s", enabled, g_sMapName);
}

public Action Command_SetAutoBhop(int client, int args)
{
    if (g_db == null)
    {
        ReplyToCommand(client, "[autobhop_enabler] Database not connected. Command unavailable.");
        return Plugin_Handled;
    }

    // If client is not 0, it's a player, so perform in-game checks.
    if (client != 0 && !IsClientInGame(client))
    {
        return Plugin_Handled;
    }

    if (args > 1)
    {
        ReplyToCommand(client, "[autobhop_enabler] Usage: sm_setautobhop [map_name]");
        return Plugin_Handled;
    }

    char targetMapName[PLATFORM_MAX_PATH];
    if (args == 1)
    {
        GetCmdArg(1, targetMapName, sizeof(targetMapName));
    }
    else
    {
        strcopy(targetMapName, sizeof(targetMapName), g_sMapName);
    }

    char query[512];
    Format(query, sizeof(query), "SELECT autobhop_enabled FROM maptiers WHERE map = '%s' LIMIT 1;", targetMapName);

    DBResultSet results = SQL_Query(g_db, query);
    if (results == null)
    {
        ReplyToCommand(client, "[autobhop_enabler] Failed to query autobhop state for map %s.", targetMapName);
        return Plugin_Handled;
    }

    int currentValue = 0;
    int newValue     = 0;

    if (results.FetchRow())    // Map exists in the table
    {
        currentValue = results.FetchInt(0);
        newValue     = currentValue ? 0 : 1;    // Toggle the value

        Format(query, sizeof(query), "UPDATE maptiers SET autobhop_enabled = %d WHERE map = '%s';", newValue, targetMapName);
        if (!SQL_FastQuery(g_db, query))
        {
            ReplyToCommand(client, "[autobhop_enabler] Failed to update autobhop_enabled for map %s.", targetMapName);
            return Plugin_Handled;
        }
    }
    else    // Map does not exist in the table
    {
        ReplyToCommand(client, "[autobhop_enabler] Map '%s' not found in the database. No changes made.", targetMapName);
        return Plugin_Handled;
    }

    // If the modified map is the current map, apply cvar changes immediately
    if (StrEqual(targetMapName, g_sMapName, false))
    {
        g_cvAutoBhop.SetBool(newValue != 0);
        g_cvEnableBhop.SetBool(newValue != 0);
    }

    if (client == 0)
    {
        PrintToChatAll("[autobhop_enabler] Console toggled autobhop to %d for map %s.", newValue, targetMapName);
        LogMessage("[autobhop_enabler] Console toggled autobhop to %d for map %s.", newValue, targetMapName);
    }
    else
    {
        PrintToChatAll("[autobhop_enabler] %N toggled autobhop to %d for map %s.", client, newValue, targetMapName);
        LogMessage("[autobhop_enabler] %N toggled autobhop to %d for map %s.", client, newValue, targetMapName);
    }
    return Plugin_Handled;
}
