"Phrases"
{
	// ---------- <PERSON><PERSON>u ---------- //
	"AddMapZone"
	{
		"pt"		"Adicionar zona do mapa"
	}
	"DeleteAllMapZone"
	{
		"pt"		"Excluir TODAS as zonas do mapa"
	}
	"DeleteMapZone"
	{
		"pt"		"Excluir zona do mapa"
	}
	// --------- Set Start --------- //
	"SetStart"
	{
		"#format"	"{1:s},{2:s},{3:s},{4:s},{5:s}"
		"pt"		"A posição inicial foi {1}definida{2} para {3}{4}{5}."
	}
	"SetStartCommandAlive"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Você precisa estar {1}vivo{2} para definir sua posição inicial."
	}
	"SetStartCommandPractice"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Você {1}não pode{2} definir a posição inicial no modo de treino."
	}
	"SetStartCommandTimerPaused"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Você {1}não pode{2} definir a posição inicial quando o timer está pausado."
	}
	"DeleteSetStart"
	{
		"#format"	"{1:s},{2:s},{3:s},{4:s},{5:s}"
		"pt"		"A posição inicial de {1}{2}{3} foi {4}excluída{5}."
	}
	"DeleteSetStartMenuTitle"
	{
		"pt"        "Posição inicial para excluir"
	}
	// ---------- ZoneHook ---------- //
	"ZoneHook_Crosshair"
	{
		"pt"        "Entidade sob a mira"
	}
	"ZoneHook_Tpto"
	{
		"pt"        "Teleportar para (para o timer)"
	}
	"ZoneHook_Draw"
	{
		"pt"        "Desenhar feixes ao redor da entidade"
	}
	"ZoneHook_Zonetype"
	{
		"#format"   "{1:s}"
		"pt"        "Tipo de zona: {1}"
	}
	"ZoneHook_Hooktype"
	{
		"#format"   "{1:s},{2:s}"
		"pt"        "Tipo de hook: {1} ({2})"
	}
	"ZoneHook_Asboxform"
	{
		"pt"        "Hook trigger como forma de Caixa"
	}
	"ZoneHook_Confirm"
	{
		"pt"        "Ir para confirmar"
	}
	// ---------- Commands ---------- //
	"StageCommandAlive"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Você precisa estar {1}vivo{2} para usar este comando."
	}
	"StageCommandTimerNotRunning"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Seu timer precisa estar {1}iniciado{2} para usar este comando."
	}
	"StageCommandTimerPaused"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Seu timer precisa estar {1}retomado{2} para usar este comando."
	}
	"StageCommandInsideStageZone"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Você {1}não pode{2} usar este comando na zona de estágio atual."
	}
	"StageCommandNoStage"
	{
		"#format"	"{1:s},{2:d},{3:s}"
		"pt"		"Não há estágio {1}{2}{3} neste mapa."
	}
	"ModifierCommandNoArgs"
	{
		"pt"		"Uso: sm_modifier <número decimal>"
	}
	"ModifierTooLow"
	{
		"pt"		"O modificador deve ser maior que 0."
	}
	"ModifierSet"
	{
		"pt"		"Modificador definido para"
	}
	"ZonesCommand"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Você {1}não pode{2} configurar zonas do mapa quando está morto."
	}
	"ZoneDrawAllZoneEnabled"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Desenhar todas as zonas {1}ativado{2}."
	}
	"ZoneDrawAllZoneDisabled"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Desenhar todas as zonas {1}desativado{2}."
	}
	"ZonesNotSQL"
	{
		"#format"   "{1:s},{2:s}"
		"pt"        "Você {1}não pode{2} adicionar/editar/excluir zonas não-SQL."
	}
	"ZoneCustomSpawnMenuTitle"
	{
		"pt"		"Adicionar spawn personalizado para a pista:"
	}
	"ZoneCustomSpawnMenuDeleteTitle"
	{
		"pt"		"Excluir spawn personalizado para a pista:"
	}
	"ZoneCustomSpawnExists"
	{
		"#format"	"{1:s},{2:s},{3:s}"
		"pt"		"O Spawn Personalizado para a pista {1}{2}{3} já existe. Por favor, exclua-o antes de colocar um novo."
	}
	"ZoneCustomSpawnMissing"
	{
		"#format"	"{1:s},{2:s},{3:s}"
		"pt"		"O Spawn Personalizado para a pista {1}{2}{3} está faltando."
	}
	"ZoneCustomSpawnDelete"
	{
		"pt"		"Spawn Personalizado excluído com sucesso."
	}
	"ZoneDead"
	{
		"pt"		"Você não pode colocar zonas quando está morto."
	}
	"ZoneDeletedStopTimer"
	{
		"#format"	"{1:s},{2:s},{3:s},{4:s}"
		"pt"		"Seu timer foi forçado a parar porque {1}{2}{3} {4} foi excluído(a)."
	}
	"AllZoneDeletedStopTimer"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Seu timer foi forçado a parar porque {1}Todas as Zonas{2} foram excluídas."
	}
	// ---------- Panel Text ---------- //
	"AbortZoneCreation"
	{
		"pt"		"Abortar criação de zona"
	}
	"GridSnapPlus"
	{
		"#format"	"{1:d}"
		"pt"		"Ajuste à grade + (x{1})"
	}
	"GridSnapMinus"
	{
		"pt"		"Ajuste à grade -"
	}
	"LockZoneAxis"
	{
		"#format"	"{1:s}"
		"pt"		"Travar eixo: {1}"
	}
	"WallSnap"
	{
		"#format"	"{1:T}"
		"pt"		"Ajustar à parede: {1}"
	}
	"CursorZone"
	{
		"#format"	"{1:T}"
		"pt"		"Usar posição do cursor: {1}"
	}
	// ---------- Zone Menu ---------- //
	"ZoneAdjustCancel"
	{
		"pt"		"Cancelar"
	}
	"ZoneAdjustDone"
	{
		"pt"		"Concluído!"
	}
	"ZoneAdjustPosition"
	{
		"pt"		"Ajuste a posição da zona.\nUse 'sm_modifier <número>' para definir um novo modificador."
	}
	"ZoneAxis"
	{
		"pt"		"Mudar Eixo"
	}
	"ZoneCreateType"
	{
		"#format"	"{1:s},{2:s},{3:s},{4:s}"
		"pt"		"Criando zona: {1}{2} {3}{4}."
	}
	"ZoneCustomSpawnSuccess"
	{
		"pt"		"Spawn personalizado colocado com sucesso!"
	}
	"ZoneEditConfirm"
	{
		"pt"		"Confirmar?"
	}
	"ZoneDeleteSuccessful"
	{
		"#format"	"{1:s},{2:s},{3:s}"
		"pt"		"{1}{2}{3} excluído(a) com sucesso."
	}
	"ZoneDeleteAllSuccessful"
	{
		"pt"		"Todas as zonas do mapa foram excluídas com sucesso"
	}
	"ZoneFirst"
	{
		"pt"		"PRIMEIRO"
	}
	"ZoneMenuDeleteTitle"
	{
		"pt"		"Excluir uma zona:\nPressionar uma zona irá excluí-la. Esta ação NÃO PODE SER REVERTIDA!"
	}
	"ZoneMenuDeleteALLTitle"
	{
		"pt"		"Excluir TODAS as zonas do mapa?\nPressionar 'Sim' excluirá todas as zonas existentes para este mapa.\nEsta ação NÃO PODE SER REVERTIDA!"
	}
	"ZoneMenuNo"
	{
		"pt"		"NÃO!"
	}
	"ZonesMenuNoneFound"
	{
		"pt"		"Nenhuma zona encontrada."
	}
	"ZoneMenuStage"
	{
		"pt"		"Selecione Um Estágio:"
	}
	"ZoneMenuTrack"
	{
		"pt"		"Selecione uma pista:"
	}
	"ZoneMenuTitle"
	{
		"#format"	"{1:s}"
		"pt"		"Selecione um tipo de zona: ({1})"
	}
	"ZoneMenuYes"
	{
		"pt"		"SIM!!! EXCLUIR TODAS AS ZONAS DO MAPA!!!"
	}
	"ZonePlaceText"
	{
		"#format"	"{1:s}"
		"pt"		"Pressione USE (padrão 'E') para definir o canto {1} na sua posição atual."
	}
	"ZonePlaceTextTF2"
	{
		"#format"	"{1:s}"
		"pt"		"Pressione ATTACK2 (padrão 'botão direito do mouse') para definir o canto {1} na sua posição atual."
	}
	"ZonePoint"
	{
		"#format"	"{1:d},{2:c}"
		"pt"		"Ponto {1} | eixo {2}"
	}
	"ZoneLowHeight"
	{
		"#format"	"{1:s},{2:s},{3:.1f},{4:s}"
		"pt"		"A altura de {1} deve ser maior que {2}{3}{4}."
	}
	"ZoneSecond"
	{
		"pt"		"SEGUNDO"
	}
	"ZoneThird"
	{
		"pt"		"TERCEIRO"
	}
	"ZoneSetAdjust"
	{
		"pt"		"Ajustar posição"
	}
	"ZoneForceRender"
	{
		"pt"		"Forçar desenho da zona"
	}
	"ZoneDrawAsBox"
	{
		"pt"		"Desenhar zona como caixa"
	}
	"ZoneSetAiraccelerate"
	{
		"#format"	"{1:d}"
		"pt"		"Aceleração no ar: {1}"
	}
	"ZoneSetSpeedLimit"
	{
		"#format"	"{1:d}"
		"pt"		"Limite de velocidade personalizado: {1}"
	}
	"ZoneSetSpeedLimitUnlimited"
	{
		"#format"	"{1:d}"
		"pt"		"Limite de velocidade personalizado: {1} (Sem Limite)"
	}
	"ZoneSetStage"
	{
		"#format"	"{1:d}"
		"pt"		"Estágio: {1}"
	}
	"ZoneSpeedLimitOption"
	{
		"pt"		"Opções de Limite de Velocidade"
	}
	"SpeedLimitOptionMenuTitle"
	{
		"pt"		"Opções de Limite de Velocidade"
	}
	"ZoneSpeedLimitSettingOverridden"
	{
		"pt"		"As configurações atuais serão substituídas por limites de velocidade de nível superior."
	}
	"ZoneLimitSpeed"
	{
		"pt"		"Limitar velocidade horizontal"
	}
	"ZoneBlockBhop"
	{
		"pt"		"Bloquear Bunnyhop"
	}
	"ZoneBlockJump"
	{
		"pt"		"Bloquear Pré-pulo"
	}
	"ZoneReduceSpeed"
	{
		"pt"		"Reduzir velocidade ao exceder o limite"
	}
	"ZoneNoVerticalSpeed"
	{
		"pt"		"Iniciar timer se houver velocidade vertical"
	}
	"ZoneResetSpeedAfterTeleport"
	{
		"pt"		"Redefinir velocidade após teleporte"
	}
	"ZoneSetCheckpoint"
	{
		"#format"	"{1:d}"
		"pt"		"Checkpoint: {1}"
	}
	"ZoneSetGravity"
	{
		"#format"   "{1:f}"
		"pt"        "Escala de gravidade: {1}"
	}
	"ZoneSetSpeedmod"
	{
		"#format"   "{1:f}"
		"pt"        "Modificador de velocidade: {1}"
	}
	"ZoneEnterDataChat"
	{
		"pt"		"Insira os dados desejados no chat."
	}
	"ZoneBadInputData"
	{
		"pt"		"Dados de entrada inválidos."
	}
	"ZoneSetNo"
	{
		"pt"		"Não"
	}
	"ZoneSetTP"
	{
		"pt"		"Confirmar (escolha o destino do teleporte primeiro)"
	}
	"ZoneSetTPZone"
	{
		"pt"		"Atualizar destino do teleporte"
	}
	"ZoneSetYes"
	{
		"pt"		"Sim"
	}
	"ZoneSetConfirm"
	{
		"pt"		"Confirmar"
	}
	"ZoneSetCancel"
	{
		"pt"		"Cancelar"
	}
	"ZoneSizeIncrease"
	{
		"#format"	"{1:s},{2:c},{3:s},{4:d},{5:s},{6:.01f},{7:s}"
		"pt"		"Eixo {1}{2}{3} (ponto {4}) aumentado em {5}{6}{7}."
	}
	"ZoneSizeDecrease"
	{
		"#format"	"{1:s},{2:c},{3:s},{4:d},{5:s},{6:.01f},{7:s}"
		"pt"		"Eixo {1}{2}{3} (ponto {4}) diminuído em {5}{6}{7}."
	}
	"ZoneTeleportUpdated"
	{
		"pt"		"Destino do teleporte atualizado."
	}
	"ZoneTeleportInsideZone"
	{
		"pt"		"Você não pode colocar o destino dentro da zona."
	}
	"ZoneTeleportInsideOtherStageZone"
	{
		"pt"		"Você não pode colocar o destino dentro de outra zona de estágio."
	}
	"ZoneEditTrack"
	{
		"#format"	"{1:s}"
		"pt"		"Pista da zona: {1}"
	}
	"ZoneEdit"
	{
		"pt"		"Editar uma zona do mapa"
	}
	"HookZone"
	{
		"pt"        "Hook um gatilho, teleportador ou botão."
	}
	"HookZone2"
	{
		"#format"   "{1:s}"
		"pt"        "Hook {1}"
	}
	"ZoneEditTitle"
	{
		"pt"		"Escolha uma zona para editar:"
	}
	"ZoneInside"
	{
		"pt"		"(Dentro!)"
	}
	"ZoneEditRefresh"
	{
		"pt"		"Atualizar menu"
	}
	"TpToZone"
	{
		"pt"        "Teleportar para uma zona"
	}
	// ---------- Custom Zone ---------- //
	"CustomZoneOption"
	{
		"pt"		"Opções de Zona Personalizada"
	}
	"CustomZone_MainMenuTitle"
	{
		"pt"		"Selecione a zona que você quer personalizar"
	}
	"CustomZone_SubMenuTitle"
	{
		"#format"	"{1:s},{2:s}"
		"pt"		"Personalizando {1} - {2}"
	}
	"CustomZone_DisplayType"
	{
		"pt"		"Tipo de exibição da zona"
	}
	"CustomZone_Color"
	{
		"pt"		"Cor da zona"
	}
	"CustomZone_Width"
	{
		"pt"		"Largura da zona"
	}
	"CustomZone_Default"
	{
		"pt"		"Padrão"
	}

	"CustomZone_DisplayType_None"
	{
		"pt"		"Nenhum"
	}
	"CustomZone_DisplayType_Box"
	{
		"pt"		"Caixa"
	}
	"CustomZone_DisplayType_Flat"
	{
		"pt"		"Plano"
	}

	"CustomZone_Color_White"
	{
		"pt"		"Branco"
	}
	"CustomZone_Color_Red"
	{
		"pt"		"Vermelho"
	}
	"CustomZone_Color_Orange"
	{
		"pt"		"Laranja"
	}
	"CustomZone_Color_Yellow"
	{
		"pt"		"Amarelo"
	}
	"CustomZone_Color_Green"
	{
		"pt"		"Verde"
	}
	"CustomZone_Color_Cyan"
	{
		"pt"		"Ciano"
	}
	"CustomZone_Color_Blue"
	{
		"pt"		"Azul"
	}
	"CustomZone_Color_Purple"
	{
		"pt"		"Roxo"
	}
	"CustomZone_Color_Pink"
	{
		"pt"		"Rosa"
	}

	"CustomZone_Width_UltraThin"
	{
		"pt"		"Ultra fina"
	}
	"CustomZone_Width_Thin"
	{
		"pt"		"Fina"
	}
	"CustomZone_Width_Normal"
	{
		"pt"		"Normal"
	}
	"CustomZone_Width_Thick"
	{
		"pt"		"Grossa"
	}
	// ---------- Messages ---------- //
	"ZoneSlayEnter"
	{
		"#format"	"{1:s},{2:s},{3:s}"
		"pt"		"{1}Você foi morto por entrar em uma {2}zona de glitch{3}."
	}
	"ZoneStopEnter"
	{
		"#format"	"{1:s},{2:s},{3:s}"
		"pt"		"{1}Seu timer foi parado por entrar em uma {2}zona de glitch{3}."
	}
	"Zone_Start"
	{
		"pt"		"Zona de início"
	}
	"Zone_End"
	{
		"pt"		"Zona final"
	}
	"Zone_Respawn"
	{
		"pt"		"Zona de Glitch (Renascer Jogador)"
	}
	"Zone_Stop"
	{
		"pt"		"Zona de Glitch (Parar Timer)"
	}
	"Zone_Slay"
	{
		"pt"		"Matar Jogador"
	}
	"Zone_Freestyle"
	{
		"pt"		"Zona de Freestyle"
	}
	"Zone_CustomSpeedLimit"
	{
		"pt"		"Limite de Velocidade Personalizado"
	}
	"Zone_Teleport"
	{
		"pt"		"Zona de Teleporte"
	}
	"Zone_CustomSpawn"
	{
		"pt"		"PONTO DE SPAWN"
	}
	"Zone_Easybhop"
	{
		"pt"		"Zona de Easybhop"
	}
	"Zone_Slide"
	{
		"pt"		"Zona de Deslize"
	}
	"Zone_Airaccelerate"
	{
		"pt"		"Aceleração no Ar Personalizada"
	}
	"Zone_Stage"
	{
		"pt"		"Zona de Estágio"
	}
	"Zone_Checkpoint"
	{
		"pt"		"Zona de Checkpoint"
	}
	"Zone_NoTimerGravity"
	{
		"pt"		"Zona de Gravidade Sem Timer"
	}
	"Zone_Gravity"
	{
		"pt"		"Zona de Gravidade"
	}
	"Zone_Speedmod"
	{
		"pt"		"Zona de Modificador de Velocidade"
	}
	"Zone_NoJump"
	{
		"pt"		"Zona Sem Pulo"
	}
	"Zone_Autobhop"
	{
		"pt"		"Zona de Autobhop"
	}
	"Zone_Unknown"
	{
		"pt"		"ZONA DESCONHECIDA"
	}
}
