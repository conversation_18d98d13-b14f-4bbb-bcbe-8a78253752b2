#if defined _sh_servers_included
    #endinput
#endif
#define _sh_servers_included

#define MAX_SERVERS 32

// Global variables
stock char g_sServerName[MAX_SERVERS][64];
stock char g_sMapName[MAX_SERVERS][64];
stock char g_sPlayers[MAX_SERVERS][16];
stock char g_sAddress[MAX_SERVERS][32];
stock int  g_iServerCount = 0;

forward void OnServerListUpdated(int serverCount); // forward declaration for server list updates

public SharedPlugin __pl_sh_servers =
{
    name = "sh-servers",
    file = "sh-servers.smx",
#if defined REQUIRE_PLUGIN
    required = 1,
#else
    required = 0,
#endif
};

#if !defined REQUIRE_PLUGIN
public void __pl_sh_servers_SetNTVOptional()
{
    MarkNativeAsOptional("SH_GetServerRunningMap");
}
#endif

/**
 * Gets information about a server running the specified map
 *
 * @param mapName       The map name to search for
 * @param name          Buffer to store the server name
 * @param nameLen       Maximum length of the name buffer
 * @param address       Buffer to store the server address (ip:port)
 * @param addressLen    Maximum length of the address buffer
 * @return              True if a server running the map was found, false otherwise
 */
native bool SH_GetServerRunningMap(const char[] mapName, char[] name, int nameLen, char[] address, int addressLen);